// فئة تحتوي على جميع مسارات التطبيق (routes) كمتغيرات ثابتة (static constants)
// تُستخدم لتسهيل إدارة المسارات وتجنب الأخطاء الناتجة عن كتابة المسارات يدوياً
class AppRoutes {
  // ======================
  // المسارات الأساسية (Root routes)
  // ======================
  static const String splash = '/'; // شاشة البداية/الترحيب
  static const String dashboard = '/dashboard'; // لوحة التحكم الرئيسية
  static const String login = '/login';
  static const String home = '/home'; // شاشة البداية/الترحيب

  // ======================

  // static const String enhancedProductsPage = '/enhancedProductsPage'; // قائمة المنتجات
  // static const String addUnitDialog = 'home/addUnitDialog'; // قائمة المنتجات

  // مسارات المخزون (Inventory routes)
  // ======================
  static const String inventory = '/inventory'; // الصفحة الرئيسية للمخزون
  static const String products = '/inventory/products'; // قائمة المنتجات

  static const String productAdd = '/inventory/products/add'; // إضافة منتج جديد
  static const String productEdit = '/inventory/products/edit'; // تعديل منتج
  static const String productView =
      '/inventory/products/view'; // عرض تفاصيل المنتج
  static const String categories = '/inventory/categories'; // التصنيفات
  static const String categoryAdd = '/inventory/categories/add'; // إضافة تصنيف
  static const String categoryEdit =
      '/inventory/categories/edit'; // تعديل تصنيف
  static const String units = '/inventory/units'; // وحدات القياس
  static const String unitAdd = '/inventory/units/add'; // إضافة وحدة قياس
  static const String unitEdit = '/inventory/units/edit'; // تعديل وحدة قياس
  static const String warehouses = '/inventory/warehouses'; // المخازن
  static const String warehouseAdd = '/inventory/warehouses/add'; // إضافة مخزن
  static const String warehouseEdit =
      '/inventory/warehouses/edit'; // تعديل مخزن
  static const String warehouseView = '/inventory/warehouses/view'; // عرض مخزن
  static const String stockManagement =
      '/inventory/stock-management'; // إدارة المخزون
  static const String stockMovements =
      '/inventory/stock-movements'; // حركة المخزون
  static const String stockLog = '/inventory/stock-log'; // سجل حركة المخزون
  static const String stockAdjustment =
      '/inventory/stock-adjustment'; // تسوية المخزون
  static const String stockTransfer =
      '/inventory/stock-transfer'; // نقل المخزون
  static const String stockReports = '/inventory/reports'; // تقارير المخزون

  // ======================
  // مسارات المبيعات (Sales routes)
  // ======================
  static const String sales = '/sales'; // الصفحة الرئيسية للمبيعات
  static const String pos = '/sales/pos'; // نقطة البيع (Point of Sale)
  static const String saleAdd = '/sales/add'; // إضافة فاتورة مبيعات
  static const String saleList = '/sales/list'; // قائمة فواتير المبيعات
  static const String saleView = '/sales/view'; // عرض فاتورة مبيعات
  static const String saleEdit = '/sales/edit'; // تعديل فاتورة مبيعات
  static const String saleReturn = '/sales/return'; // مرتجع المبيعات

  // ======================
  // مسارات المشتريات (Purchase routes)
  // ======================
  static const String purchases = '/purchases'; // الصفحة الرئيسية للمشتريات
  static const String purchaseList = '/purchases/list'; // قائمة المشتريات
  static const String purchaseAdd = '/purchases/add'; // إضافة فاتورة مشتريات
  static const String purchaseEdit = '/purchases/edit'; // تعديل فاتورة مشتريات
  static const String purchaseView = '/purchases/view'; // عرض فاتورة مشتريات
  static const String purchaseReturn = '/purchases/return'; // مرتجع المشتريات

  // ======================
  // مسارات الحسابات (Accounts routes)
  // ======================
  static const String accounts = '/accounts'; // الصفحة الرئيسية للحسابات
  static const String customers = '/accounts/customers'; // العملاء
  static const String customerAdd = '/accounts/customers/add'; // إضافة عميل
  static const String customerEdit = '/accounts/customers/edit'; // تعديل عميل
  static const String customerView =
      '/accounts/customers/view'; // عرض بيانات العميل
  static const String suppliers = '/accounts/suppliers'; // الموردين
  static const String supplierAdd = '/accounts/suppliers/add'; // إضافة مورد
  static const String supplierEdit = '/accounts/suppliers/edit'; // تعديل مورد
  static const String supplierView =
      '/accounts/suppliers/view'; // عرض بيانات المورد

  // ======================
  // مسارات الصناديق (Cash routes)
  // ======================
  static const String cash = '/cash'; // الصفحة الرئيسية للصناديق
  static const String cashBoxes = '/cash/boxes'; // الصناديق النقدية
  static const String cashBoxAdd = '/cash/boxes/add'; // إضافة صندوق
  static const String cashBoxEdit = '/cash/boxes/edit'; // تعديل صندوق
  static const String transactions = '/cash/transactions'; // المعاملات المالية
  static const String transactionAdd = '/cash/transactions/add'; // إضافة معاملة
  static const String transactionView = '/cash/transactions/view'; // عرض معاملة
  static const String receipt = '/cash/receipt'; // سند قبض
  static const String payment = '/cash/payment'; // سند صرف

  // ======================
  // مسارات الموظفين (Employee routes)
  // ======================
  static const String employees = '/employees'; // الصفحة الرئيسية للموظفين
  static const String employeeList = '/employees/list'; // قائمة الموظفين
  static const String employeeAdd = '/employees/add'; // إضافة موظف
  static const String employeeEdit = '/employees/edit'; // تعديل موظف
  static const String employeeView = '/employees/view'; // عرض بيانات الموظف
  static const String salaries = '/employees/salaries'; // الرواتب
  static const String salaryAdd = '/employees/salaries/add'; // إضافة راتب
  static const String salaryView =
      '/employees/salaries/view'; // عرض بيانات الراتب
  static const String salaryList = '/employees/salaries/list'; // قائمة الرواتب

  // ======================
  // مسارات التقارير (Reports routes)
  // ======================
  static const String reports = '/reports'; // الصفحة الرئيسية للتقارير
  static const String salesReport = '/reports/sales'; // تقرير المبيعات
  static const String purchaseReport = '/reports/purchases'; // تقرير المشتريات
  static const String inventoryReport = '/reports/inventory'; // تقرير المخزون
  static const String customerReport = '/reports/customers'; // تقرير العملاء
  static const String supplierReport = '/reports/suppliers'; // تقرير الموردين
  static const String cashFlowReport =
      '/reports/cash-flow'; // تقرير التدفق النقدي
  static const String profitLossReport =
      '/reports/profit-loss'; // تقرير الأرباح والخسائر
  static const String taxReport = '/reports/tax'; // تقرير الضرائب

  // ======================
  // مسارات الإعدادات (Settings routes)
  // ======================
  static const String settings = '/settings'; // الصفحة الرئيسية للإعدادات
  static const String userManagement = '/settings/users'; // إدارة المستخدمين
  static const String userAdd = '/settings/users/add'; // إضافة مستخدم
  static const String userEdit = '/settings/users/edit'; // تعديل مستخدم
  static const String companyInfo = '/settings/company'; // معلومات الشركة
  static const String printerSettings = '/settings/printer'; // إعدادات الطابعة
  static const String taxSettings = '/settings/tax'; // إعدادات الضرائب
  static const String backupRestore =
      '/settings/backup'; // النسخ الاحتياطي والاستعادة
  static const String branches = '/settings/branches'; // الفروع
  static const String branchAdd = '/settings/branches/add'; // إضافة فرع
  static const String branchEdit = '/settings/branches/edit'; // تعديل فرع

  // ======================
  // مسارات الذكاء الاصطناعي (AI routes)
  // ======================
  static const String aiDashboard =
      '/ai-dashboard'; // لوحة تحكم الذكاء الاصطناعي

  // ======================
  // مسارات أخرى (Utility routes)
  // ======================
  static const String sync = '/sync'; // مزامنة البيانات
  static const String profile = '/profile'; // الملف الشخصي
  static const String about = '/about'; // حول التطبيق
  static const String help = '/help'; // المساعدة

  // ======================
  // معلمات المسارات (Route parameters)
  // ======================
  static const String idParam = 'id'; // معرّف العنصر (مستخدم في معظم المسارات)
  static const String typeParam = 'type'; // نوع العنصر
  static const String modeParam = 'mode'; // الوضع (عرض/تعديل/إضافة)

  // ======================
  // دوال لإنشاء مسارات ديناميكية مع المعرّفات
  // ======================
  static String productEditWithId(String id) =>
      '$productEdit/$id'; // مسار تعديل منتج مع المعرّف
  static String productViewWithId(String id) =>
      '$productView/$id'; // مسار عرض منتج مع المعرّف
  static String categoryEditWithId(String id) =>
      '$categoryEdit/$id'; // مسار تعديل تصنيف مع المعرّف
  static String unitEditWithId(String id) =>
      '$unitEdit/$id'; // مسار تعديل وحدة قياس مع المعرّف
  static String warehouseEditWithId(String id) =>
      '$warehouseEdit/$id'; // مسار تعديل مخزن مع المعرّف
  static String warehouseViewWithId(String id) =>
      '$warehouseView/$id'; // مسار عرض مخزن مع المعرّف
  static String saleViewWithId(String id) =>
      '$saleView/$id'; // مسار عرض فاتورة مبيعات مع المعرّف
  static String saleEditWithId(String id) =>
      '$saleEdit/$id'; // مسار تعديل فاتورة مبيعات مع المعرّف
  static String purchaseViewWithId(String id) =>
      '$purchaseView/$id'; // مسار عرض فاتورة مشتريات مع المعرّف
  static String purchaseEditWithId(String id) =>
      '$purchaseEdit/$id'; // مسار تعديل فاتورة مشتريات مع المعرّف
  static String customerViewWithId(String id) =>
      '$customerView/$id'; // مسار عرض عميل مع المعرّف
  static String customerEditWithId(String id) =>
      '$customerEdit/$id'; // مسار تعديل عميل مع المعرّف
  static String supplierViewWithId(String id) =>
      '$supplierView/$id'; // مسار عرض مورد مع المعرّف
  static String supplierEditWithId(String id) =>
      '$supplierEdit/$id'; // مسار تعديل مورد مع المعرّف
  static String employeeViewWithId(String id) =>
      '$employeeView/$id'; // مسار عرض موظف مع المعرّف
  static String employeeEditWithId(String id) =>
      '$employeeEdit/$id'; // مسار تعديل موظف مع المعرّف
  static String cashBoxEditWithId(String id) =>
      '$cashBoxEdit/$id'; // مسار تعديل صندوق مع المعرّف
  static String transactionViewWithId(String id) =>
      '$transactionView/$id'; // مسار عرض معاملة مع المعرّف
  static String salaryViewWithId(String id) =>
      '$salaryView/$id'; // مسار عرض راتب مع المعرّف
  static String userEditWithId(String id) =>
      '$userEdit/$id'; // مسار تعديل مستخدم مع المعرّف
  static String branchEditWithId(String id) =>
      '$branchEdit/$id'; // مسار تعديل فرع مع المعرّف

  // ======================
  // مجموعات المسارات للتنقل (Route groups for navigation)
  // ======================
  static const List<String> mainRoutes = [
    dashboard,
    inventory,
    sales,
    purchases,
    accounts,
    cash,
    employees,
    reports,
    settings,
  ];

  static const List<String> inventoryRoutes = [
    products,
    categories,
    units,
    warehouses,
    stockManagement,
    stockMovements,
    stockLog,
    stockAdjustment,
    stockTransfer,
    stockReports,
  ];

  static const List<String> salesRoutes = [
    pos,
    saleAdd,
    saleList,
  ];

  static const List<String> purchaseRoutes = [
    purchaseList,
    purchaseAdd,
  ];

  static const List<String> accountRoutes = [
    customers,
    suppliers,
  ];

  static const List<String> cashRoutes = [
    cashBoxes,
    transactions,
  ];

  static const List<String> employeeRoutes = [
    employeeList,
    salaries,
  ];

  static const List<String> reportRoutes = [
    salesReport,
    purchaseReport,
    inventoryReport,
    customerReport,
    supplierReport,
    cashFlowReport,
    profitLossReport,
    taxReport,
  ];

  static const List<String> settingRoutes = [
    userManagement,
    companyInfo,
    printerSettings,
    taxSettings,
    backupRestore,
    branches,
  ];

  // ======================
  // دوال مساعدة (Helper methods)
  // ======================

  // التحقق من نوع المسار
  static bool isMainRoute(String route) => mainRoutes.contains(route);
  static bool isInventoryRoute(String route) => route.startsWith(inventory);
  static bool isSalesRoute(String route) => route.startsWith(sales);
  static bool isPurchaseRoute(String route) => route.startsWith(purchases);
  static bool isAccountRoute(String route) => route.startsWith(accounts);
  static bool isCashRoute(String route) => route.startsWith(cash);
  static bool isEmployeeRoute(String route) => route.startsWith(employees);
  static bool isReportRoute(String route) => route.startsWith(reports);
  static bool isSettingRoute(String route) => route.startsWith(settings);

  // الحصول على عنوان المسار (باللغة العربية)
  static String getRouteTitle(String route) {
    switch (route) {
      case dashboard:
        return 'لوحة التحكم';
      case inventory:
        return 'المخزون';
      case products:
        return 'المنتجات';
      case categories:
        return 'التصنيفات';
      case units:
        return 'وحدات القياس';
      case warehouses:
        return 'المخازن';
      case stockManagement:
        return 'إدارة المخزون';
      case stockMovements:
        return 'حركة المخزون';
      case stockTransfer:
        return 'نقل المخزون';
      case stockReports:
        return 'تقارير المخزون';
      case sales:
        return 'المبيعات';
      case pos:
        return 'نقطة البيع';
      case saleAdd:
        return 'إضافة فاتورة مبيعات';
      case saleList:
        return 'قائمة المبيعات';
      case purchases:
        return 'المشتريات';
      case purchaseList:
        return 'قائمة المشتريات';
      case accounts:
        return 'الحسابات';
      case customers:
        return 'العملاء';
      case suppliers:
        return 'الموردين';
      case cash:
        return 'الصناديق';
      case cashBoxes:
        return 'الصناديق';
      case transactions:
        return 'المعاملات';
      case employees:
        return 'الموظفين';
      case employeeList:
        return 'قائمة الموظفين';
      case salaries:
        return 'الرواتب';
      case reports:
        return 'التقارير';
      case settings:
        return 'الإعدادات';
      default:
        return 'تجاري تك'; // الاسم الافتراضي للتطبيق
    }
  }
}
