import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:uuid/uuid.dart';
import '../constants/app_constants.dart';
// Import missing classes
import 'dart:async';
import 'dart:math';

class AppUtils {
  static const _uuid = Uuid();
  
  // Generate unique ID
  static String generateId() => _uuid.v4();
  
  // Generate invoice number
  static String generateInvoiceNumber(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final number = timestamp % 100000000; // Get last 8 digits
    return '$prefix${number.toString().padLeft(8, '0')}';
  }
  
  // Vibrate device
  static void vibrate() {
    HapticFeedback.lightImpact();
  }
  
  // Heavy vibrate
  static void vibrateHeavy() {
    HapticFeedback.heavyImpact();
  }
  
  // Selection vibrate
  static void vibrateSelection() {
    HapticFeedback.selectionClick();
  }
  
  // Hide keyboard
  static void hideKeyboard(BuildContext context) {
    FocusScope.of(context).unfocus();
  }
  
  // Show keyboard
  static void showKeyboard(BuildContext context, FocusNode focusNode) {
    FocusScope.of(context).requestFocus(focusNode);
  }
  
  // Copy to clipboard
  static Future<void> copyToClipboard(String text) async {
    await Clipboard.setData(ClipboardData(text: text));
  }
  
  // Get clipboard data
  static Future<String?> getClipboardData() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    return data?.text;
  }
  
  // Check if string is null or empty
  static bool isNullOrEmpty(String? value) {
    return value == null || value.trim().isEmpty;
  }
  
  // Check if list is null or empty
  static bool isListNullOrEmpty<T>(List<T>? list) {
    return list == null || list.isEmpty;
  }
  
  // Safe string conversion
  static String safeString(dynamic value) {
    if (value == null) return '';
    return value.toString();
  }
  
  // Safe double conversion
  static double safeDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }
  
  // Safe int conversion
  static int safeInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value) ?? 0;
    return 0;
  }
  
  // Safe bool conversion
  static bool safeBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is int) return value != 0;
    if (value is String) {
      final lower = value.toLowerCase();
      return lower == 'true' || lower == '1' || lower == 'yes';
    }
    return false;
  }
  
  // Calculate percentage
  static double calculatePercentage(double value, double total) {
    if (total == 0) return 0.0;
    return (value / total) * 100;
  }
  
  // Calculate percentage value
  static double calculatePercentageValue(double total, double percentage) {
    return (total * percentage) / 100;
  }
  
  // Round to decimal places
  static double roundToDecimalPlaces(double value, int decimalPlaces) {
    final factor = pow(10, decimalPlaces);
    return (value * factor).round() / factor;
  }
  
  // Check if email is valid
  static bool isValidEmail(String email) {
    return RegExp(AppConstants.emailRegex).hasMatch(email);
  }
  
  // Check if phone is valid
  static bool isValidPhone(String phone) {
    return RegExp(AppConstants.phoneRegex).hasMatch(phone);
  }
  
  // Check if barcode is valid
  static bool isValidBarcode(String barcode) {
    return RegExp(AppConstants.barcodeRegex).hasMatch(barcode);
  }
  
  // Generate random color
  static Color generateRandomColor() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return Color((random & 0xFFFFFF) | 0xFF000000);
  }
  
  // Get contrast color (black or white)
  static Color getContrastColor(Color color) {
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
  
  // Debounce function
  static Timer? _debounceTimer;
  static void debounce(VoidCallback callback, {Duration delay = const Duration(milliseconds: 500)}) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }
  
  // Throttle function
  static DateTime? _lastThrottleTime;
  static void throttle(VoidCallback callback, {Duration delay = const Duration(milliseconds: 500)}) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || now.difference(_lastThrottleTime!) >= delay) {
      _lastThrottleTime = now;
      callback();
    }
  }
  
  // Format file size
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  // Get device type
  static DeviceType getDeviceType(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 768) return DeviceType.mobile;
    if (screenWidth < 1024) return DeviceType.tablet;
    return DeviceType.desktop;
  }
  
  // Check if device is mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }
  
  // Check if device is tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }
  
  // Check if device is desktop
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.desktop;
  }
  
  // Get app version
  static Future<String> getAppVersion() async {
    // This would typically use package_info_plus
    return AppConstants.appVersion;
  }
  
  // Log debug message
  static void logDebug(String message) {
    debugPrint('[DEBUG] $message');
  }
  
  // Log error message
  static void logError(String message, [dynamic error, StackTrace? stackTrace]) {
    debugPrint('[ERROR] $message');
    if (error != null) debugPrint('[ERROR] Error: $error');
    if (stackTrace != null) debugPrint('[ERROR] StackTrace: $stackTrace');
  }
  
  // Log info message
  static void logInfo(String message) {
    debugPrint('[INFO] $message');
  }
  
  // Log warning message
  static void logWarning(String message) {
    debugPrint('[WARNING] $message');
  }
  
  // Convert Arabic numbers to English
  static String arabicToEnglishNumbers(String text) {
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    const englishNumbers = '0123456789';
    
    String result = text;
    for (int i = 0; i < arabicNumbers.length; i++) {
      result = result.replaceAll(arabicNumbers[i], englishNumbers[i]);
    }
    return result;
  }
  
  // Convert English numbers to Arabic
  static String englishToArabicNumbers(String text) {
    const englishNumbers = '0123456789';
    const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
    
    String result = text;
    for (int i = 0; i < englishNumbers.length; i++) {
      result = result.replaceAll(englishNumbers[i], arabicNumbers[i]);
    }
    return result;
  }
  
  // Clean phone number
  static String cleanPhoneNumber(String phone) {
    // Remove all non-digit characters
    String cleaned = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Add country code if needed
    if (cleaned.length == 9 && cleaned.startsWith('5')) {
      cleaned = '966$cleaned';
    } else if (cleaned.length == 10 && cleaned.startsWith('05')) {
      cleaned = '966${cleaned.substring(1)}';
    }
    
    return cleaned;
  }
  
  // Format phone number for display
  static String formatPhoneForDisplay(String phone) {
    final cleaned = cleanPhoneNumber(phone);
    if (cleaned.length == 12 && cleaned.startsWith('966')) {
      return '+${cleaned.substring(0, 3)} ${cleaned.substring(3, 4)}${cleaned.substring(4, 5)} ${cleaned.substring(5, 8)} ${cleaned.substring(8)}';
    }
    return phone;
  }
}

// Device type enum
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

