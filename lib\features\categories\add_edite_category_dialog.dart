import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tijari_tech/data/models/category.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/category_provider.dart';

/*
class AddCategoryDialog extends ConsumerStatefulWidget {
  const AddCategoryDialog({super.key});

  @override
  ConsumerState<AddCategoryDialog> createState() => _AddCategoryDialogState();
}

class _AddCategoryDialogState extends ConsumerState<AddCategoryDialog> {

  final _formKey = GlobalKey<FormState>();
  final _nameArController = TextEditingController();
  final _nameEnController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isMainCategory = true;   // <ـ الجديد

  String? _selectedParentId;
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameArController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(mainCategoriesProvider);

    return Dialog(
      child: Container(
        width: 500.w,
        height: 500.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.category,
                  color: AppColors.primary,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'إضافة فئة جديدة',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Basic Information
                      TextFormField(
                        controller: _nameArController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الفئة (عربي) *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'اسم الفئة مطلوب';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),

                      TextFormField(
                        controller: _nameEnController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الفئة (إنجليزي)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(height: 16.h),

                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                      SizedBox(height: 16.h),
                      CheckboxListTile(
                        title: const Text('فئة رئيسية'),
                        value: _isMainCategory,
                        onChanged: (val) =>
                            setState(() => _isMainCategory = val!),
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      SizedBox(height: 16.h),

                      if (!_isMainCategory) // إظهار الـ dropdown فقط إذا لم تكن رئيسية
                        categoriesAsync.when(
                          data: (categories) => DropdownButtonFormField<String>(
                            value: _selectedParentId,
                            decoration: const InputDecoration(
                              labelText: 'الفئة الأب',
                              border: OutlineInputBorder(),
                            ),
                            items: categories
                                .map((c) => DropdownMenuItem(
                                    value: c.id,
                                    child: Text(c.getDisplayName())))
                                .toList(),
                            onChanged: (val) =>
                                setState(() => _selectedParentId = val),
                          ),
                          loading: () => const CircularProgressIndicator(),
                          error: (e, s) => Text('خطأ: $e'),
                        ),
                      SizedBox(height: 16.h),

                      CheckboxListTile(
                        title: const Text('فئة نشطة'),
                        value: _isActive,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value ?? true;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveCategory,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveCategory() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await ref
          .read(enhancedCategoryManagementProvider.notifier)
          .createCategory(
            nameAr: _nameArController.text.trim(),
            nameEn: _nameEnController.text.trim().isEmpty
                ? null
                : _nameEnController.text.trim(),
            description: _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
            parentId: _isMainCategory ? null : _selectedParentId,
            isActive: _isActive,
          );

      if (result) {
        Navigator.of(context).pop();
        context.showSuccessSnackBar('تم إضافة الفئة بنجاح');
      } else {
        context.showErrorSnackBar('فشل في إضافة الفئة');
      }
    } catch (e) {
      context.showErrorSnackBar('حدث خطأ: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
*/
class AddEditedCategoryDialog extends ConsumerStatefulWidget {
  final bool isEdit;
  final String? categoryId; // معرف الفئة للتعديل
  final String? parentCategoryId; // معرف الفئة الأب لإضافة فئة فرعية

  const AddEditedCategoryDialog({
    super.key,
    this.isEdit = false,
    this.categoryId,
    this.parentCategoryId, // إضافة معرف الفئة الأب
  });

  @override
  ConsumerState<AddEditedCategoryDialog> createState() =>
      _AddEditedCategoryDialogState();
}

class _AddEditedCategoryDialogState
    extends ConsumerState<AddEditedCategoryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameArController = TextEditingController();
  final _nameEnController = TextEditingController();
  final _descriptionController = TextEditingController();

  bool _isMainCategory = true;
  String? _selectedParentId;
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // إذا كان تعديل، تحميل بيانات الفئة
    if (widget.isEdit && widget.categoryId != null) {
      _loadCategory();
    }

    // إذا كان إضافة فئة فرعية، تعيين الفئة الأب
    if (widget.parentCategoryId != null) {
      _selectedParentId = widget.parentCategoryId;
      _isMainCategory = false; // ليست فئة رئيسية
    }
  }

  Future<void> _loadCategory() async {
    try {
      final category = await ref
          .read(enhancedCategoryManagementProvider.notifier)
          .getCategoryById(widget.categoryId!);

      if (category != null && mounted) {
        setState(() {
          _nameArController.text = category.nameAr;
          _nameEnController.text = category.nameEn ?? '';
          _descriptionController.text = category.description ?? '';
          _selectedParentId = category.parentId;
          _isMainCategory = category.parentId == null;
          _isActive = category.isActive;
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم العثور على الفئة'),
              backgroundColor: Colors.red,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات الفئة: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameArController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoriesAsync = ref.watch(mainCategoriesProvider);

    return Dialog(
      child: Container(
        width: 500.w,
        height: 500.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  widget.isEdit ? Icons.edit : Icons.category,
                  color: AppColors.primary,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  widget.isEdit ? 'تعديل الفئة' : 'إضافة فئة جديدة',
                  style: AppTextStyles.titleLarge
                      .copyWith(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _nameArController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الفئة (عربي) *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) =>
                            value == null || value.trim().isEmpty
                                ? 'اسم الفئة مطلوب'
                                : null,
                      ),
                      SizedBox(height: 16.h),
                      TextFormField(
                        controller: _nameEnController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الفئة (إنجليزي)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(height: 16.h),
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'الوصف',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                      ),
                      SizedBox(height: 16.h),
                      CheckboxListTile(
                        title: const Text('فئة رئيسية'),
                        value: _isMainCategory,
                        onChanged: (val) =>
                            setState(() => _isMainCategory = val!),
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                      SizedBox(height: 16.h),
                      if (!_isMainCategory)
                        categoriesAsync.when(
                          data: (categories) {
                            final filtered =
                                widget.isEdit && widget.categoryId != null
                                    ? categories
                                        .where((c) => c.id != widget.categoryId)
                                        .toList()
                                    : categories;

                            return DropdownButtonFormField<String>(
                              value: _selectedParentId,
                              decoration: const InputDecoration(
                                labelText: 'الفئة الأب',
                                border: OutlineInputBorder(),
                              ),
                              items: filtered
                                  .map((c) => DropdownMenuItem(
                                        value: c.id,
                                        child: Text(c.getDisplayName()),
                                      ))
                                  .toList(),
                              onChanged: (val) =>
                                  setState(() => _selectedParentId = val),
                            );
                          },
                          loading: () => const CircularProgressIndicator(),
                          error: (e, s) => Text('خطأ: $e'),
                        ),
                      SizedBox(height: 16.h),
                      CheckboxListTile(
                        title: const Text('فئة نشطة'),
                        value: _isActive,
                        onChanged: (value) =>
                            setState(() => _isActive = value ?? true),
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _submit,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2))
                        : Text(widget.isEdit ? 'تحديث' : 'حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final notifier = ref.read(enhancedCategoryManagementProvider.notifier);
      final success = widget.isEdit
          ? await notifier.updateCategory(
              id: widget.categoryId!,
              nameAr: _nameArController.text.trim(),
              nameEn: _nameEnController.text.trim().isEmpty
                  ? null
                  : _nameEnController.text.trim(),
              description: _descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim(),
              parentId: _isMainCategory ? null : _selectedParentId,
              isActive: _isActive,
            )
          : await notifier.createCategory(
              nameAr: _nameArController.text.trim(),
              nameEn: _nameEnController.text.trim().isEmpty
                  ? null
                  : _nameEnController.text.trim(),
              description: _descriptionController.text.trim().isEmpty
                  ? null
                  : _descriptionController.text.trim(),
              parentId: _isMainCategory ? null : _selectedParentId,
              isActive: _isActive,
            );

      if (mounted) {
        if (success) {
          Navigator.of(context).pop();
          context.showSuccessSnackBar(
              widget.isEdit ? 'تم التعديل بنجاح' : 'تم الإضافة بنجاح');
        } else {
          context.showErrorSnackBar(
              widget.isEdit ? 'فشل في التعديل' : 'فشل في الإضافة');
        }
      }
    } catch (e) {
      if (mounted) {
        context.showErrorSnackBar('حدث خطأ: ${e.toString()}');
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
