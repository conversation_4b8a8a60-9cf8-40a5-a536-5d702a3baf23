import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'secure_storage_service.dart';

/// اختبار خدمة التخزين الآمن
class StorageTest {
  static final _storage = SecureStorageService();

  /// تشغيل جميع الاختبارات
  static Future<void> runAllTests() async {
    if (kDebugMode) {
      print('🧪 بدء اختبار خدمة التخزين الآمن...');
      
      try {
        await _testBasicOperations();
        await _testJsonOperations();
        await _testListOperations();
        await _testBooleanOperations();
        await _testNumberOperations();
        await _testIntegrityCheck();
        await _testStorageInfo();
        
        print('✅ جميع الاختبارات نجحت!');
      } catch (e) {
        print('❌ فشل في الاختبار: $e');
      }
    }
  }

  /// اختبار العمليات الأساسية
  static Future<void> _testBasicOperations() async {
    print('📝 اختبار العمليات الأساسية...');
    
    // كتابة وقراءة نص
    const testKey = 'test_string';
    const testValue = 'مرحبا بك في تجاري تك';
    
    await _storage.write(key: testKey, value: testValue);
    final readValue = await _storage.read(key: testKey);
    
    assert(readValue == testValue, 'فشل في قراءة النص');
    
    // التحقق من وجود المفتاح
    final exists = await _storage.containsKey(key: testKey);
    assert(exists, 'المفتاح غير موجود');
    
    // حذف المفتاح
    await _storage.delete(key: testKey);
    final deletedValue = await _storage.read(key: testKey);
    assert(deletedValue == null, 'فشل في حذف المفتاح');
    
    print('✅ العمليات الأساسية نجحت');
  }

  /// اختبار عمليات JSON
  static Future<void> _testJsonOperations() async {
    print('📝 اختبار عمليات JSON...');
    
    const testKey = 'test_json';
    final testValue = {
      'id': '123',
      'name': 'أحمد محمد',
      'email': '<EMAIL>',
      'isActive': true,
      'balance': 1500.75,
      'roles': ['admin', 'user'],
    };
    
    await _storage.writeJson(key: testKey, value: testValue);
    final readValue = await _storage.readJson(key: testKey);
    
    assert(readValue != null, 'فشل في قراءة JSON');
    assert(readValue!['id'] == testValue['id'], 'بيانات JSON غير صحيحة');
    assert(readValue?['name'] == testValue['name'], 'بيانات JSON غير صحيحة');
    
    await _storage.delete(key: testKey);
    print('✅ عمليات JSON نجحت');
  }

  /// اختبار عمليات القوائم
  static Future<void> _testListOperations() async {
    print('📝 اختبار عمليات القوائم...');
    
    const testKey = 'test_list';
    const testValue = ['منتج 1', 'منتج 2', 'منتج 3'];
    
    await _storage.writeList(key: testKey, value: testValue);
    final readValue = await _storage.readList(key: testKey);
    
    assert(readValue != null, 'فشل في قراءة القائمة');
    assert(readValue!.length == testValue.length, 'طول القائمة غير صحيح');
    assert(readValue?[0] == testValue[0], 'عناصر القائمة غير صحيحة');
    
    await _storage.delete(key: testKey);
    print('✅ عمليات القوائم نجحت');
  }

  /// اختبار العمليات المنطقية
  static Future<void> _testBooleanOperations() async {
    print('📝 اختبار العمليات المنطقية...');
    
    const testKey = 'test_bool';
    
    // اختبار true
    await _storage.writeBool(key: testKey, value: true);
    final trueValue = await _storage.readBool(key: testKey);
    assert(trueValue == true, 'فشل في قراءة true');
    
    // اختبار false
    await _storage.writeBool(key: testKey, value: false);
    final falseValue = await _storage.readBool(key: testKey);
    assert(falseValue == false, 'فشل في قراءة false');
    
    await _storage.delete(key: testKey);
    print('✅ العمليات المنطقية نجحت');
  }

  /// اختبار عمليات الأرقام
  static Future<void> _testNumberOperations() async {
    print('📝 اختبار عمليات الأرقام...');
    
    // اختبار الأرقام الصحيحة
    const intKey = 'test_int';
    const intValue = 12345;
    
    await _storage.writeInt(key: intKey, value: intValue);
    final readIntValue = await _storage.readInt(key: intKey);
    assert(readIntValue == intValue, 'فشل في قراءة الرقم الصحيح');
    
    // اختبار الأرقام العشرية
    const doubleKey = 'test_double';
    const doubleValue = 123.456;
    
    await _storage.writeDouble(key: doubleKey, value: doubleValue);
    final readDoubleValue = await _storage.readDouble(key: doubleKey);
    assert(readDoubleValue == doubleValue, 'فشل في قراءة الرقم العشري');
    
    await _storage.delete(key: intKey);
    await _storage.delete(key: doubleKey);
    print('✅ عمليات الأرقام نجحت');
  }

  /// اختبار سلامة البيانات
  static Future<void> _testIntegrityCheck() async {
    print('📝 اختبار سلامة البيانات...');
    
    final isValid = await _storage.verifyIntegrity();
    assert(isValid, 'فشل في التحقق من سلامة البيانات');
    
    print('✅ اختبار سلامة البيانات نجح');
  }

  /// اختبار معلومات التخزين
  static Future<void> _testStorageInfo() async {
    print('📝 اختبار معلومات التخزين...');
    
    // إضافة بعض البيانات للاختبار
    await _storage.write(key: 'info_test_1', value: 'قيمة 1');
    await _storage.write(key: 'info_test_2', value: 'قيمة 2');
    
    final info = await _storage.getStorageInfo();
    
    assert(info['encryption_enabled'] == true, 'التشفير غير مفعل');
    assert(info['total_keys'] >= 2, 'عدد المفاتيح غير صحيح');
    assert(info['storage_type'] != null, 'نوع التخزين غير محدد');
    
    // تنظيف البيانات
    await _storage.delete(key: 'info_test_1');
    await _storage.delete(key: 'info_test_2');
    
    print('✅ اختبار معلومات التخزين نجح');
  }

  /// اختبار الأداء
  static Future<void> testPerformance() async {
    if (kDebugMode) {
      print('⚡ اختبار الأداء...');
      
      final stopwatch = Stopwatch()..start();
      
      // كتابة 100 عنصر
      for (int i = 0; i < 100; i++) {
        await _storage.write(key: 'perf_test_$i', value: 'قيمة $i');
      }
      
      final writeTime = stopwatch.elapsedMilliseconds;
      stopwatch.reset();
      
      // قراءة 100 عنصر
      for (int i = 0; i < 100; i++) {
        await _storage.read(key: 'perf_test_$i');
      }
      
      final readTime = stopwatch.elapsedMilliseconds;
      stopwatch.reset();
      
      // حذف 100 عنصر
      for (int i = 0; i < 100; i++) {
        await _storage.delete(key: 'perf_test_$i');
      }
      
      final deleteTime = stopwatch.elapsedMilliseconds;
      stopwatch.stop();
      
      print('📊 نتائج الأداء:');
      print('   - كتابة 100 عنصر: ${writeTime}ms');
      print('   - قراءة 100 عنصر: ${readTime}ms');
      print('   - حذف 100 عنصر: ${deleteTime}ms');
      print('✅ اختبار الأداء مكتمل');
    }
  }

  /// اختبار التشفير المتقدم
  static Future<void> testAdvancedEncryption() async {
    if (kDebugMode) {
      print('🔐 اختبار التشفير المتقدم...');
      
      // بيانات حساسة للاختبار
      final sensitiveData = {
        'password': 'كلمة_مرور_سرية_123',
        'api_key': 'sk-1234567890abcdef',
        'credit_card': '1234-5678-9012-3456',
        'personal_id': '1234567890',
      };
      
      const testKey = 'sensitive_data';
      
      // حفظ البيانات الحساسة
      await _storage.writeJson(key: testKey, value: sensitiveData);
      
      // قراءة البيانات
      final readData = await _storage.readJson(key: testKey);
      
      assert(readData != null, 'فشل في قراءة البيانات الحساسة');
      assert(readData!['password'] == sensitiveData['password'], 'كلمة المرور غير صحيحة');
      assert(readData?['api_key'] == sensitiveData['api_key'], 'مفتاح API غير صحيح');
      
      // تنظيف البيانات
      await _storage.delete(key: testKey);
      
      print('✅ اختبار التشفير المتقدم نجح');
    }
  }
}
