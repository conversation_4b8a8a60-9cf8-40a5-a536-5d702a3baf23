import 'package:sqflite/sqflite.dart' as sql;
import 'package:tijari_tech/core/constants/database_constants.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'base_dao.dart';
import '../../../core/utils/app_utils.dart';

class UnitDao extends BaseDao<Unit> {
  @override
  String get tableName => DatabaseConstants.tableUnits;

  @override
  Unit fromMap(Map<String, dynamic> map) => Unit.fromMap(map);

  @override
  Map<String, dynamic> toMap(Unit entity) => entity.toMap();

  // ------------------------------------------------------------------
  // Unit-specific queries
  // ------------------------------------------------------------------

  /// البحث عن الوحدات حسب الاسم
  Future<List<Unit>> searchByName(String searchTerm) async {
    return await findWhere(
      where: '${DatabaseConstants.columnUnitName} LIKE ?',
      whereArgs: ['%$searchTerm%'],
      orderBy: DatabaseConstants.columnUnitName,
    );
  }

  /// التحقق من استخدام الوحدة في المنتجات
  Future<bool> isUsedByProducts(String unitId) async {
    try {
      final db = await database;

      // التحقق من جدول المنتجات
      final productResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableProducts} 
        WHERE ${DatabaseConstants.columnProductBaseUnitId} = ? 
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''', [unitId]);

      if ((productResult.first['count'] as int) > 0) return true;

      // التحقق من جدول أسعار المنتجات
      final priceResult = await db.rawQuery('''
        SELECT COUNT(*) as count 
        FROM ${DatabaseConstants.tableProductPrices} 
        WHERE ${DatabaseConstants.columnProductPriceUnitId} = ? 
          AND ${DatabaseConstants.columnProductPriceDeletedAt} IS NULL
      ''', [unitId]);

      return (priceResult.first['count'] as int) > 0;
    } catch (e) {
      AppUtils.logError('Error checking if unit is used by products', e);
      return false;
    }
  }

  /// استرجاع الوحدات الأساسية (factor = 1.0)
  Future<List<Unit>> getBaseUnits() async {
    return await findWhere(
      where: '${DatabaseConstants.columnUnitFactor} = 1.0',
      orderBy: DatabaseConstants.columnUnitName,
    );
  }

  /// استرجاع الوحدات التحويلية (factor ≠ 1.0)
  Future<List<Unit>> getConversionUnits() async {
    return await findWhere(
      where: '${DatabaseConstants.columnUnitFactor} != 1.0',
      orderBy: DatabaseConstants.columnUnitFactor,
    );
  }

  /// تحويل الكمية بين الوحدات
  double convertQuantity(double quantity, double fromFactor, double toFactor) {
    final baseQuantity = quantity * fromFactor;
    return baseQuantity / toFactor;
  }

  /// الحصول على معدل التحويل بين وحدتين
  Future<double?> getConversionRate(String fromUnitId, String toUnitId) async {
    try {
      if (fromUnitId == toUnitId) return 1.0;

      final fromUnit = await findById(fromUnitId);
      final toUnit = await findById(toUnitId);

      if (fromUnit == null || toUnit == null) return null;

      return fromUnit.factor / toUnit.factor;
    } catch (e) {
      AppUtils.logError('Error getting conversion rate', e);
      return null;
    }
  }

  /// التحقق من تفرّد اسم الوحدة
  Future<bool> isNameUnique(String name, {String? excludeUnitId}) async {
    try {
      String where = '${DatabaseConstants.columnUnitName} = ? '
          'AND ${DatabaseConstants.columnUnitDeletedAt} IS NULL';
      List<dynamic> whereArgs = [name];

      if (excludeUnitId != null) {
        where += ' AND ${DatabaseConstants.columnUnitId} != ?';
        whereArgs.add(excludeUnitId);
      }

      final count = await this.count(where: where, whereArgs: whereArgs);
      return count == 0;
    } catch (e) {
      AppUtils.logError('Error checking unit name uniqueness', e);
      return false;
    }
  }

  /// استرجاع إحصاءات الوحدة
  Future<Map<String, dynamic>?> getUnitStatistics(String unitId) async {
    final sql = '''
      SELECT 
        u.${DatabaseConstants.columnUnitId},
        u.${DatabaseConstants.columnUnitName},
        u.${DatabaseConstants.columnUnitFactor},
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COUNT(DISTINCT pp.${DatabaseConstants.columnProductPriceId}) as price_count,
        COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) as sale_item_count,
        COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId}) as purchase_item_count,
        COALESCE(SUM(si.${DatabaseConstants.columnSaleItemQty}), 0) as total_sold,
        COALESCE(SUM(pi.${DatabaseConstants.columnPurchaseItemQty}), 0) as total_purchased
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableProductPrices} pp 
        ON u.${DatabaseConstants.columnUnitId} = pp.${DatabaseConstants.columnProductPriceUnitId} 
       AND pp.${DatabaseConstants.columnProductPriceDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON u.${DatabaseConstants.columnUnitId} = si.${DatabaseConstants.columnSaleItemUnitId}
      LEFT JOIN ${DatabaseConstants.tablePurchaseItems} pi 
        ON u.${DatabaseConstants.columnUnitId} = pi.${DatabaseConstants.columnPurchaseItemUnitId}
      WHERE u.${DatabaseConstants.columnUnitId} = ? 
        AND u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}, 
               u.${DatabaseConstants.columnUnitName}, 
               u.${DatabaseConstants.columnUnitFactor}
    ''';

    final result = await rawQuery(sql, [unitId]);
    return result.isNotEmpty ? result.first : null;
  }

  /// استرجاع أكثر الوحدات استخداماً
  Future<List<Map<String, dynamic>>> getMostUsedUnits({int limit = 10}) async {
    final sql = '''
      SELECT 
        u.*,
        COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) as product_count,
        COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) as sale_count,
        COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId}) as purchase_count,
        (
          COUNT(DISTINCT p.${DatabaseConstants.columnProductId}) + 
          COUNT(DISTINCT si.${DatabaseConstants.columnSaleItemId}) + 
          COUNT(DISTINCT pi.${DatabaseConstants.columnPurchaseItemId})
        ) as total_usage
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p 
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId} 
       AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      LEFT JOIN ${DatabaseConstants.tableSaleItems} si 
        ON u.${DatabaseConstants.columnUnitId} = si.${DatabaseConstants.columnSaleItemUnitId}
      LEFT JOIN ${DatabaseConstants.tablePurchaseItems} pi 
        ON u.${DatabaseConstants.columnUnitId} = pi.${DatabaseConstants.columnPurchaseItemUnitId}
      WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}
      ORDER BY total_usage DESC
      LIMIT ?
    ''';

    return await rawQuery(sql, [limit]);
  }

  /// حذف وحدة مع التحقق
  Future<bool> deleteUnitSafely(String unitId) async {
    try {
      final isUsed = await isUsedByProducts(unitId);
      if (isUsed) {
        throw Exception('Cannot delete unit: it is being used by products');
      }

      final db = await database;
      final transactionCount = await db.rawQuery('''
        SELECT COUNT(*) as count FROM (
          SELECT 1 FROM ${DatabaseConstants.tableSaleItems} 
            WHERE ${DatabaseConstants.columnSaleItemUnitId} = ?
          UNION ALL
          SELECT 1 FROM ${DatabaseConstants.tablePurchaseItems} 
            WHERE ${DatabaseConstants.columnPurchaseItemUnitId} = ?
          UNION ALL
          SELECT 1 FROM ${DatabaseConstants.tableStockMovements} 
            WHERE ${DatabaseConstants.columnStockMovementUnitId} = ?
        )
      ''', [unitId, unitId, unitId]);

      if ((transactionCount.first['count'] as int) > 0) {
        throw Exception('Cannot delete unit: it has transaction history');
      }

      return await delete(unitId);
    } catch (e) {
      AppUtils.logError('Error deleting unit safely', e);
      return false;
    }
  }

  /// استرجاع الوحدات للقائمة المنسدلة
  Future<List<Map<String, dynamic>>> getUnitsForSelection() async {
    final sql = '''
      SELECT 
        ${DatabaseConstants.columnUnitId} as id,
        ${DatabaseConstants.columnUnitName} as name,
        ${DatabaseConstants.columnUnitFactor} as factor,
        CASE 
          WHEN ${DatabaseConstants.columnUnitFactor} = 1.0 
            THEN ${DatabaseConstants.columnUnitName}
          ELSE ${DatabaseConstants.columnUnitName} || ' (x' || ${DatabaseConstants.columnUnitFactor} || ')'
        END as display_name
      FROM ${DatabaseConstants.tableUnits}
      WHERE ${DatabaseConstants.columnUnitDeletedAt} IS NULL
      ORDER BY ${DatabaseConstants.columnUnitFactor}, ${DatabaseConstants.columnUnitName}
    ''';

    return await rawQuery(sql);
  }

  /// تحديث عوامل التحويل بالجملة
  Future<bool> bulkUpdateFactors(Map<String, double> factorUpdates) async {
    try {
      final db = await database;
      final batch = db.batch();
      final now = DateTime.now().toIso8601String();

      factorUpdates.forEach((unitId, factor) {
        batch.update(
          tableName,
          {
            DatabaseConstants.columnUnitFactor: factor,
            DatabaseConstants.columnUnitUpdatedAt: now,
            DatabaseConstants.columnUnitIsSynced: 0,
          },
          where:
              '${DatabaseConstants.columnUnitId} = ? AND ${DatabaseConstants.columnUnitDeletedAt} IS NULL',
          whereArgs: [unitId],
        );
      });

      await batch.commit(noResult: true);
      AppUtils.logInfo(
          'Bulk updated factors for ${factorUpdates.length} units');
      return true;
    } catch (e) {
      AppUtils.logError('Error bulk updating factors', e);
      return false;
    }
  }

  /// جدول تحويل الوحدات
  Future<List<Map<String, dynamic>>> getUnitConversionTable() async {
    final sql = '''
      SELECT 
        u1.${DatabaseConstants.columnUnitId} as from_unit_id,
        u1.${DatabaseConstants.columnUnitName} as from_unit_name,
        u1.${DatabaseConstants.columnUnitFactor} as from_factor,
        u2.${DatabaseConstants.columnUnitId} as to_unit_id,
        u2.${DatabaseConstants.columnUnitName} as to_unit_name,
        u2.${DatabaseConstants.columnUnitFactor} as to_factor,
        (u1.${DatabaseConstants.columnUnitFactor} / u2.${DatabaseConstants.columnUnitFactor}) as conversion_rate
      FROM ${DatabaseConstants.tableUnits} u1
      CROSS JOIN ${DatabaseConstants.tableUnits} u2
      WHERE u1.${DatabaseConstants.columnUnitDeletedAt} IS NULL 
        AND u2.${DatabaseConstants.columnUnitDeletedAt} IS NULL
        AND u1.${DatabaseConstants.columnUnitId} != u2.${DatabaseConstants.columnUnitId}
      ORDER BY u1.${DatabaseConstants.columnUnitName}, u2.${DatabaseConstants.columnUnitName}
    ''';

    return await rawQuery(sql);
  }

  /// التحقق من صحة عامل التحويل
  bool isValidFactor(double factor) {
    return factor > 0 && factor.isFinite;
  }

  /// استرجاع الوحدة الافتراضية
  Future<Unit?> getDefaultUnit() async {
    final units = await findWhere(
      where: '${DatabaseConstants.columnUnitFactor} = 1.0',
      orderBy: DatabaseConstants.columnUnitCreatedAt,
      limit: 1,
    );
    return units.isNotEmpty ? units.first : null;
  }

  // ------------------------------------------------------------------
  // Enhanced Unit-specific queries (merged from EnhancedUnitDao)
  // ------------------------------------------------------------------

  /// استرجاع الوحدات المشتقة من وحدة أساسية
  Future<List<Unit>> findDerivedUnits(String baseUnitId) async {
    return await findWhere(
      where:
          'base_unit_id = ? AND ${DatabaseConstants.columnUnitDeletedAt} IS NULL',
      whereArgs: [baseUnitId],
      orderBy: DatabaseConstants.columnUnitName,
    );
  }

  /// استرجاع عدد استخدامات الوحدة
  Future<int> getUsageCount(String unitId) async {
    try {
      final sql = '''
        SELECT COUNT(*) as count
        FROM ${DatabaseConstants.tableProducts}
        WHERE ${DatabaseConstants.columnProductBaseUnitId} = ?
          AND ${DatabaseConstants.columnProductDeletedAt} IS NULL
      ''';

      final result = await rawQuery(sql, [unitId]);
      return result.isNotEmpty ? (result.first['count'] as int) : 0;
    } catch (e) {
      AppUtils.logError('Error getting usage count', e);
      return 0;
    }
  }

  /// استرجاع أنواع الوحدات المتاحة
  Future<List<String>> getUnitTypes() async {
    try {
      final sql = '''
        SELECT DISTINCT ${DatabaseConstants.columnUnitType}
        FROM ${DatabaseConstants.tableUnits}
        WHERE ${DatabaseConstants.columnUnitDeletedAt} IS NULL
        ORDER BY ${DatabaseConstants.columnUnitType}
      ''';

      final result = await rawQuery(sql);
      return result
          .map((row) => row[DatabaseConstants.columnUnitType] as String)
          .toList();
    } catch (e) {
      AppUtils.logError('Error getting unit types', e);
      return [];
    }
  }

  /// استرجاع الوحدات مع عدد الاستخدامات
  Future<List<Unit>> getUnitsWithUsageCount() async {
    final sql = '''
      SELECT
        u.*,
        COUNT(p.${DatabaseConstants.columnProductId}) as usage_count
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId}
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      WHERE u.${DatabaseConstants.columnUnitDeletedAt} IS NULL
      GROUP BY u.${DatabaseConstants.columnUnitId}
      ORDER BY u.${DatabaseConstants.columnUnitName}
    ''';

    final result = await rawQuery(sql);
    return result.map((map) => Unit.fromMap(map)).toList();
  }

  /// البحث المتقدم في الوحدات
  Future<List<Unit>> advancedSearch({
    String? searchTerm,
    String? unitType,
    bool? isActive,
    bool? isBaseUnit,
    String? orderBy,
    bool ascending = true,
    int? limit,
    int? offset,
  }) async {
    final conditions = <String>[];
    final args = <dynamic>[];

    // Always exclude deleted units
    conditions.add('${DatabaseConstants.columnUnitDeletedAt} IS NULL');

    if (searchTerm != null && searchTerm.isNotEmpty) {
      conditions.add('''
        (${DatabaseConstants.columnUnitName} LIKE ?
         OR symbol LIKE ?
         OR abbreviation LIKE ?)
      ''');
      final searchPattern = '%$searchTerm%';
      args.addAll([searchPattern, searchPattern, searchPattern]);
    }

    if (unitType != null) {
      conditions.add('${DatabaseConstants.columnUnitType} = ?');
      args.add(unitType);
    }

    if (isActive != null) {
      conditions.add('${DatabaseConstants.columnUnitIsActive} = ?');
      args.add(isActive ? 1 : 0);
    }

    if (isBaseUnit != null) {
      conditions.add('${DatabaseConstants.columnUnitIsBaseUnit} = ?');
      args.add(isBaseUnit ? 1 : 0);
    }

    final whereClause = conditions.join(' AND ');

    final sql = '''
      SELECT
        u.*,
        COUNT(p.${DatabaseConstants.columnProductId}) as usage_count
      FROM ${DatabaseConstants.tableUnits} u
      LEFT JOIN ${DatabaseConstants.tableProducts} p
        ON u.${DatabaseConstants.columnUnitId} = p.${DatabaseConstants.columnProductBaseUnitId}
        AND p.${DatabaseConstants.columnProductDeletedAt} IS NULL
      WHERE $whereClause
      GROUP BY u.${DatabaseConstants.columnUnitId}
      ORDER BY ${orderBy ?? DatabaseConstants.columnUnitName} ${ascending ? 'ASC' : 'DESC'}
      ${limit != null ? 'LIMIT $limit' : ''}
      ${offset != null ? 'OFFSET $offset' : ''}
    ''';

    final result = await rawQuery(sql, args);
    return result.map((map) => Unit.fromMap(map)).toList();
  }

  /// استرجاع إحصائيات الوحدات
  Future<Map<String, dynamic>> getUnitsStatistics() async {
    final sql = '''
      SELECT
        COUNT(*) as total_units,
        COUNT(CASE WHEN ${DatabaseConstants.columnUnitIsActive} = 1 THEN 1 END) as active_units,
        COUNT(CASE WHEN ${DatabaseConstants.columnUnitIsBaseUnit} = 1 THEN 1 END) as base_units,
        COUNT(DISTINCT ${DatabaseConstants.columnUnitType}) as unit_types_count,
        AVG(${DatabaseConstants.columnUnitFactor}) as avg_factor
      FROM ${DatabaseConstants.tableUnits}
      WHERE ${DatabaseConstants.columnUnitDeletedAt} IS NULL
    ''';

    final result = await rawQuery(sql);
    return result.isNotEmpty ? result.first : {};
  }
}
