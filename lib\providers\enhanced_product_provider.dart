import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/product.dart';
import '../data/repositories/product_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Product repository provider
final enhancedProductRepositoryProvider = Provider<ProductRepository>((ref) {
  return ProductRepository();
});

// Product management provider
final enhancedProductManagementProvider = StateNotifierProvider<
    EnhancedProductManagementNotifier, EnhancedProductManagementState>((ref) {
  return EnhancedProductManagementNotifier(ref);
});

// Product management state
class EnhancedProductManagementState {
  final bool isLoading;
  final String? error;
  final List<Product> products;
  final List<Product> filteredProducts;
  final String searchQuery;
  final String? selectedCategory;
  final String? selectedUnit;
  final String stockFilter;
  final double? minPrice;
  final double? maxPrice;
  final bool showInactive;
  final Product? selectedProduct;
  final Map<String, dynamic> statistics;
  final String sortField;
  final bool sortAscending;

  const EnhancedProductManagementState({
    this.isLoading = false,
    this.error,
    this.products = const [],
    this.filteredProducts = const [],
    this.searchQuery = '',
    this.selectedCategory,
    this.selectedUnit,
    this.stockFilter = 'all',
    this.minPrice,
    this.maxPrice,
    this.showInactive = false,
    this.selectedProduct,
    this.statistics = const {},
    this.sortField = 'name',
    this.sortAscending = true,
  });

  EnhancedProductManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Product>? products,
    List<Product>? filteredProducts,
    String? searchQuery,
    String? selectedCategory,
    String? selectedUnit,
    String? stockFilter,
    double? minPrice,
    double? maxPrice,
    bool? showInactive,
    Product? selectedProduct,
    Map<String, dynamic>? statistics,
    String? sortField,
    bool? sortAscending,
  }) {
    return EnhancedProductManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      products: products ?? this.products,
      filteredProducts: filteredProducts ?? this.filteredProducts,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      selectedUnit: selectedUnit ?? this.selectedUnit,
      stockFilter: stockFilter ?? this.stockFilter,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      showInactive: showInactive ?? this.showInactive,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      statistics: statistics ?? this.statistics,
      sortField: sortField ?? this.sortField,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

// Enhanced Product management notifier
class EnhancedProductManagementNotifier
    extends StateNotifier<EnhancedProductManagementState> {
  final Ref _ref;

  EnhancedProductManagementNotifier(this._ref)
      : super(const EnhancedProductManagementState()) {
    _loadProducts();
  }

  ProductRepository get _repository =>
      _ref.read(enhancedProductRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load all products
  Future<void> _loadProducts() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final products = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAllProductsWithDetails();
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getProductsStatistics();
      });

      state = state.copyWith(
        isLoading: false,
        products: products,
        filteredProducts: _filterAndSortProducts(products),
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading products', e);
    }
  }

  // Filter and sort products based on current state
  List<Product> _filterAndSortProducts(List<Product> products) {
    var filtered = products;

    // Filter by search query
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered.where((product) {
        return product.nameAr.toLowerCase().contains(query) ||
            (product.nameEn?.toLowerCase().contains(query) ?? false) ||
            (product.barcode?.toLowerCase().contains(query) ?? false) ||
            (product.sku?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Filter by category
    if (state.selectedCategory != null) {
      filtered = filtered
          .where((product) => product.categoryId == state.selectedCategory)
          .toList();
    }

    // Filter by unit
    if (state.selectedUnit != null) {
      filtered = filtered
          .where((product) => product.baseUnitId == state.selectedUnit)
          .toList();
    }

    // Filter by stock status
    switch (state.stockFilter) {
      case 'available':
        filtered = filtered
            .where((product) => !product.isOutOfStock && !product.isLowStock)
            .toList();
        break;
      case 'low':
        filtered = filtered.where((product) => product.isLowStock).toList();
        break;
      case 'out':
        filtered = filtered.where((product) => product.isOutOfStock).toList();
        break;
    }

    // Filter by price range
    if (state.minPrice != null) {
      filtered = filtered
          .where((product) => product.sellingPrice >= state.minPrice!)
          .toList();
    }
    if (state.maxPrice != null) {
      filtered = filtered
          .where((product) => product.sellingPrice <= state.maxPrice!)
          .toList();
    }

    // Filter by active status
    if (!state.showInactive) {
      filtered = filtered.where((product) => product.isActiveProduct).toList();
    }

    // Sort products
    filtered.sort((a, b) {
      int comparison = 0;

      switch (state.sortField) {
        case 'name':
          comparison = a.nameAr.compareTo(b.nameAr);
          break;
        case 'barcode':
          comparison = (a.barcode ?? '').compareTo(b.barcode ?? '');
          break;
        case 'cost_price':
          comparison = a.costPrice.compareTo(b.costPrice);
          break;
        case 'selling_price':
          comparison = a.sellingPrice.compareTo(b.sellingPrice);
          break;
        case 'stock':
          comparison = (a.currentStock ?? 0).compareTo(b.currentStock ?? 0);
          break;
        default:
          comparison = a.nameAr.compareTo(b.nameAr);
      }

      return state.sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  // Search products
  void searchProducts(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredProducts: _filterAndSortProducts(state.products),
    );
  }

  // Apply filters
  void applyFilters({
    String? categoryId,
    String? unitId,
    String? stockFilter,
    double? minPrice,
    double? maxPrice,
    bool? showInactive,
  }) {
    state = state.copyWith(
      selectedCategory: categoryId,
      selectedUnit: unitId,
      stockFilter: stockFilter ?? state.stockFilter,
      minPrice: minPrice,
      maxPrice: maxPrice,
      showInactive: showInactive ?? state.showInactive,
      filteredProducts: _filterAndSortProducts(state.products),
    );
  }

  // Sort products
  void sortProducts(String field, bool ascending) {
    state = state.copyWith(
      sortField: field,
      sortAscending: ascending,
      filteredProducts: _filterAndSortProducts(state.products),
    );
  }

  // Select product
  void selectProduct(Product? product) {
    state = state.copyWith(selectedProduct: product);
  }

  // Refresh products
  Future<void> refresh() async {
    await _loadProducts();
    _ref.invalidate(enhancedProductRepositoryProvider);
  }

  // Create new product
  Future<bool> createProduct({
    required String nameAr,
    String? nameEn,
    String? barcode,
    String? description,
    String? categoryId,
    String? baseUnitId,
    required double costPrice,
    required double sellingPrice,
    double minStock = 0,
    double maxStock = 0,
    double reorderPoint = 0,
    bool trackStock = true,
    bool isActive = true,
    String? sku,
    DateTime? expiryDate, // إضافة دعم تاريخ الانتهاء
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final product = Product.create(
        id: AppUtils.generateId(),
        nameAr: nameAr,
        nameEn: nameEn,
        barcode: barcode,
        description: description,
        categoryId: categoryId,
        baseUnitId: baseUnitId,
        costPrice: costPrice,
        sellingPrice: sellingPrice,
        minStock: minStock,
        maxStock: maxStock,
        reorderPoint: reorderPoint,
        trackStock: trackStock,
        isActive: isActive,
        sku: sku,
      );

      await _repository.createProduct(product);
      await refresh();
      AppUtils.logInfo('Product created successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating product', e);
      return false;
    }
  }

  // Update product
  Future<bool> updateProduct({
     String? id,
    String? nameAr,
    String? nameEn,
    String? barcode,
    String? description,
    String? categoryId,
    String? baseUnitId,
    double? costPrice,
    double? sellingPrice,
    double? minStock,
    double? maxStock,
    double? reorderPoint,
    bool? trackStock,
    bool? isActive,
    String? sku,
    DateTime? expiryDate, // إضافة دعم تاريخ الانتهاء
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final existingProduct = await _repository.getProductWithDetails(id!);
      if (existingProduct == null) {
        throw Exception('Product not found');
      }

      final updatedProduct = existingProduct.update(
        nameAr: nameAr,
        nameEn: nameEn,
        barcode: barcode,
        description: description,
        categoryId: categoryId,
        baseUnitId: baseUnitId,
        costPrice: costPrice,
        sellingPrice: sellingPrice,
        minStock: minStock,
        maxStock: maxStock,
        reorderPoint: reorderPoint,
        trackStock: trackStock,
        isActive: isActive,
        sku: sku,
      );

      await _repository.updateProduct(id, updatedProduct);
      await refresh();
      AppUtils.logInfo('Product updated successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating product', e);
      return false;
    }
  }

  // Delete product
  Future<bool> deleteProduct(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.deleteProduct(id);
      await refresh();
      AppUtils.logInfo('Product deleted successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting product', e);
      return false;
    }
  }

  // Bulk delete products
  Future<bool> bulkDeleteProducts(List<String> productIds) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      for (final id in productIds) {
        await _repository.deleteProduct(id);
      }

      await refresh();
      AppUtils.logInfo('Products deleted successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error bulk deleting products', e);
      return false;
    }
  }

  // Get product by ID
  Future<Product?> getProductById(String id) async {
    try {
      return await _repository.getProductWithDetails(id);
    } catch (e) {
      AppUtils.logError('Error getting product by ID', e);
      return null;
    }
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedCategory: null,
      selectedUnit: null,
      stockFilter: 'all',
      minPrice: null,
      maxPrice: null,
      showInactive: false,
      filteredProducts: _filterAndSortProducts(state.products),
    );
  }

  // Validate data integrity
  Future<List<Map<String, dynamic>>> validateDataIntegrity() async {
    try {
      // For now, return empty list as this functionality needs to be implemented
      return [];
    } catch (e) {
      AppUtils.logError('Error validating data integrity', e);
      return [];
    }
  }

  // Fix data integrity issues
  Future<bool> fixDataIntegrityIssues() async {
    try {
      // For now, return true as this functionality needs to be implemented
      return true;
    } catch (e) {
      AppUtils.logError('Error fixing data integrity issues', e);
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Product by ID provider
final enhancedProductByIdProvider =
    FutureProvider.family<Product?, String>((ref, id) async {
  final repository = ref.read(enhancedProductRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getProductWithDetails(id);
  });
});

// Low stock products provider
final lowStockProductsProvider = FutureProvider<List<Product>>((ref) async {
  final repository = ref.read(enhancedProductRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getLowStockProducts();
  });
});

// Out of stock products provider
final outOfStockProductsProvider = FutureProvider<List<Product>>((ref) async {
  final repository = ref.read(enhancedProductRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getOutOfStockProducts();
  });
});

// Products statistics provider
final productsStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(enhancedProductRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getProductsStatistics();
  });
});
