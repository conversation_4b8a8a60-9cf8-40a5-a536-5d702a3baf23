import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/unit_provider.dart';

class AddUnitDialog extends ConsumerStatefulWidget {
  const AddUnitDialog({super.key});

  @override
  ConsumerState<AddUnitDialog> createState() => _AddUnitDialogState();
}

class _AddUnitDialogState extends ConsumerState<AddUnitDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameArController = TextEditingController();
  final _nameEnController = TextEditingController();
  final _symbolController = TextEditingController();
  final _conversionFactorController = TextEditingController();

  UnitType _selectedType = UnitType.count;
  String? _selectedBaseUnitId;
  bool _isActive = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameArController.dispose();
    _nameEnController.dispose();
    _symbolController.dispose();
    _conversionFactorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final unitsState = ref.watch(enhancedUnitManagementProvider);

    return Dialog(
      child: Container(
        width: 500.w,
        height: 600.h,
        padding: EdgeInsets.all(24.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.straighten,
                  color: AppColors.primary,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'إضافة وحدة جديدة',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Basic Information
                      TextFormField(
                        controller: _nameArController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الوحدة (عربي) *',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'اسم الوحدة مطلوب';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),

                      TextFormField(
                        controller: _nameEnController,
                        decoration: const InputDecoration(
                          labelText: 'اسم الوحدة (إنجليزي)',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      SizedBox(height: 16.h),

                      TextFormField(
                        controller: _symbolController,
                        decoration: const InputDecoration(
                          labelText: 'الرمز *',
                          border: OutlineInputBorder(),
                          hintText: 'مثل: كجم، قطعة، لتر',
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'رمز الوحدة مطلوب';
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Unit Type
                      DropdownButtonFormField<UnitType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'نوع الوحدة *',
                          border: OutlineInputBorder(),
                        ),
                        items: UnitType.values
                            .map((type) => DropdownMenuItem(
                                  value: type,
                                  child: Text(_getUnitTypeName(type)),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedType = value ?? UnitType.count;
                            // Reset base unit when type changes
                            _selectedBaseUnitId = null;
                            _conversionFactorController.clear();
                          });
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Base Unit (for conversion units)
                      DropdownButtonFormField<String>(
                        value: _selectedBaseUnitId,
                        decoration: const InputDecoration(
                          labelText: 'الوحدة الأساسية (للتحويل)',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem(
                            value: null,
                            child: Text('وحدة أساسية'),
                          ),
                          ...unitsState.units
                              .where((unit) =>
                                  unit.unitType == _selectedType &&
                                  unit.baseUnitId == null)
                              .map((unit) => DropdownMenuItem(
                                    value: unit.id,
                                    child: Text(unit.displayName),
                                  )),
                        ],
                        onChanged: (value) {
                          setState(() {
                            _selectedBaseUnitId = value;
                            if (value == null) {
                              _conversionFactorController.clear();
                            }
                          });
                        },
                      ),
                      SizedBox(height: 16.h),

                      // Conversion Factor (if base unit is selected)
                      if (_selectedBaseUnitId != null) ...[
                        TextFormField(
                          controller: _conversionFactorController,
                          decoration: const InputDecoration(
                            labelText: 'معامل التحويل *',
                            border: OutlineInputBorder(),
                            hintText: 'مثل: 0.001 (للجرام إلى كيلوجرام)',
                          ),
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (_selectedBaseUnitId != null) {
                              if (value == null || value.trim().isEmpty) {
                                return 'معامل التحويل مطلوب';
                              }
                              if (double.tryParse(value) == null) {
                                return 'أدخل رقم صحيح';
                              }
                              if (double.parse(value) <= 0) {
                                return 'معامل التحويل يجب أن يكون أكبر من صفر';
                              }
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 16.h),
                      ],

                      CheckboxListTile(
                        title: const Text('وحدة نشطة'),
                        value: _isActive,
                        onChanged: (value) {
                          setState(() {
                            _isActive = value ?? true;
                          });
                        },
                        controlAffinity: ListTileControlAffinity.leading,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            SizedBox(height: 16.h),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('إلغاء'),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveUnit,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('حفظ'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getUnitTypeName(UnitType type) {
    switch (type) {
      case UnitType.weight:
        return 'وزن';
      case UnitType.length:
        return 'طول';
      case UnitType.volume:
        return 'حجم';
      case UnitType.area:
        return 'مساحة';
      case UnitType.count:
        return 'عدد';
      case UnitType.time:
        return 'وقت';
      case UnitType.other:
        return 'أخرى';
    }
  }

  Future<void> _saveUnit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result =
          await ref.read(enhancedUnitManagementProvider.notifier).createUnit(
                nameAr: _nameArController.text.trim(),
                // nameEn: _nameEnController.text.trim().isEmpty
                //     ? null
                //     : _nameEnController.text.trim(),
                symbol: _symbolController.text.trim(),
                type: _selectedType,
                baseUnitId: _selectedBaseUnitId,
                conversionFactor: _selectedBaseUnitId != null &&
                        _conversionFactorController.text.isNotEmpty
                    ? double.parse(_conversionFactorController.text)
                    : null,
                isActive: _isActive, 
              );

      if (result) {
        Navigator.of(context).pop();
        context.showSuccessSnackBar('تم إضافة الوحدة بنجاح');
      } else {
        context.showErrorSnackBar('فشل في إضافة الوحدة');
      }
    } catch (e) {
      context.showErrorSnackBar('حدث خطأ: ${e.toString()}');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

// unit_type.dart
enum UnitType {
  weight, // وزن
  length, // طول
  volume, // حجم
  area, // مساحة
  count, // عدد
  time, // وقت
  other // أخرى
}
