import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../data/models/stock.dart';
import '../data/repositories/stock_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Stock repository provider
final stockRepositoryProvider = Provider<StockRepository>((ref) {
  return StockRepository();
});

// Stock management provider
final stockManagementProvider = StateNotifierProvider<
    StockManagementNotifier, StockManagementState>((ref) {
  return StockManagementNotifier(ref);
});

// Stock management state
class StockManagementState {
  final bool isLoading;
  final String? error;
  final List<Stock> stocks;
  final List<Stock> filteredStocks;
  final String searchQuery;
  final String? selectedWarehouse;
  final String? selectedProduct;
  final String? selectedCategory;
  final StockStatus? statusFilter;
  final Stock? selectedStock;
  final Map<String, dynamic> statistics;
  final String sortField;
  final bool sortAscending;

  const StockManagementState({
    this.isLoading = false,
    this.error,
    this.stocks = const [],
    this.filteredStocks = const [],
    this.searchQuery = '',
    this.selectedWarehouse,
    this.selectedProduct,
    this.selectedCategory,
    this.statusFilter,
    this.selectedStock,
    this.statistics = const {},
    this.sortField = 'product_name',
    this.sortAscending = true,
  });

  StockManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Stock>? stocks,
    List<Stock>? filteredStocks,
    String? searchQuery,
    String? selectedWarehouse,
    String? selectedProduct,
    String? selectedCategory,
    StockStatus? statusFilter,
    Stock? selectedStock,
    Map<String, dynamic>? statistics,
    String? sortField,
    bool? sortAscending,
  }) {
    return StockManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      stocks: stocks ?? this.stocks,
      filteredStocks: filteredStocks ?? this.filteredStocks,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedWarehouse: selectedWarehouse ?? this.selectedWarehouse,
      selectedProduct: selectedProduct ?? this.selectedProduct,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      statusFilter: statusFilter ?? this.statusFilter,
      selectedStock: selectedStock ?? this.selectedStock,
      statistics: statistics ?? this.statistics,
      sortField: sortField ?? this.sortField,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

// Stock management notifier
class StockManagementNotifier extends StateNotifier<StockManagementState> {
  final Ref _ref;

  StockManagementNotifier(this._ref) : super(const StockManagementState());

  StockRepository get _repository => _ref.read(stockRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load stocks
  Future<void> loadStocks() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final stocks = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getAllStocks();
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getStockStatistics();
      });

      state = state.copyWith(
        isLoading: false,
        stocks: stocks,
        filteredStocks: _filterAndSortStocks(stocks),
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading stocks', e);
    }
  }

  // Filter and sort stocks based on current state
  List<Stock> _filterAndSortStocks(List<Stock> stocks) {
    var filtered = stocks.where((stock) {
      // Search filter
      if (state.searchQuery.isNotEmpty) {
        final query = state.searchQuery.toLowerCase();
        if (!(stock.productName?.toLowerCase().contains(query) ?? false) &&
            !(stock.productBarcode?.toLowerCase().contains(query) ?? false) &&
            !(stock.warehouseName?.toLowerCase().contains(query) ?? false)) {
          return false;
        }
      }

      // Warehouse filter
      if (state.selectedWarehouse != null && 
          stock.warehouseId != state.selectedWarehouse) {
        return false;
      }

      // Product filter
      if (state.selectedProduct != null && 
          stock.productId != state.selectedProduct) {
        return false;
      }

      // Status filter
      if (state.statusFilter != null && 
          stock.stockStatus != state.statusFilter) {
        return false;
      }

      return true;
    }).toList();

    // Sort
    filtered.sort((a, b) {
      int comparison = 0;
      switch (state.sortField) {
        case 'product_name':
          comparison = (a.productName ?? '').compareTo(b.productName ?? '');
          break;
        case 'warehouse_name':
          comparison = (a.warehouseName ?? '').compareTo(b.warehouseName ?? '');
          break;
        case 'quantity':
          comparison = a.quantity.compareTo(b.quantity);
          break;
        case 'available_quantity':
          comparison = a.actualAvailableQuantity.compareTo(b.actualAvailableQuantity);
          break;
        case 'stock_value':
          comparison = a.stockValue.compareTo(b.stockValue);
          break;
        case 'status':
          comparison = a.stockStatus.index.compareTo(b.stockStatus.index);
          break;
        default:
          comparison = (a.productName ?? '').compareTo(b.productName ?? '');
      }
      return state.sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  // Refresh data
  Future<void> refresh() async {
    await loadStocks();
  }

  // Search stocks
  void searchStocks(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredStocks: _filterAndSortStocks(state.stocks),
    );
  }

  // Filter by warehouse
  void filterByWarehouse(String? warehouseId) {
    state = state.copyWith(
      selectedWarehouse: warehouseId,
      filteredStocks: _filterAndSortStocks(state.stocks),
    );
  }

  // Filter by product
  void filterByProduct(String? productId) {
    state = state.copyWith(
      selectedProduct: productId,
      filteredStocks: _filterAndSortStocks(state.stocks),
    );
  }

  // Filter by status
  void filterByStatus(StockStatus? status) {
    state = state.copyWith(
      statusFilter: status,
      filteredStocks: _filterAndSortStocks(state.stocks),
    );
  }

  // Sort stocks
  void sortStocks(String field, bool ascending) {
    state = state.copyWith(
      sortField: field,
      sortAscending: ascending,
      filteredStocks: _filterAndSortStocks(state.stocks),
    );
  }

  // Clear filters
  void clearFilters() {
    state = state.copyWith(
      searchQuery: '',
      selectedWarehouse: null,
      selectedProduct: null,
      selectedCategory: null,
      statusFilter: null,
      filteredStocks: _filterAndSortStocks(state.stocks),
    );
  }

  // Select stock
  void selectStock(Stock? stock) {
    state = state.copyWith(selectedStock: stock);
  }

  // Update stock quantity
  Future<void> updateStockQuantity({
    required String stockId,
    required double newQuantity,
    double? newReservedQuantity,
    double? newAverageCost,
    String? reason,
    required String userId,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final stock = state.stocks.firstWhere((s) => s.id == stockId);
      
      await _repository.updateStockWithMovement(
        productId: stock.productId,
        warehouseId: stock.warehouseId,
        movementType: 'adjustment',
        quantity: newQuantity,
        unitCost: newAverageCost ?? stock.averageCost,
        referenceType: 'manual_adjustment',
        referenceId: AppUtils.generateId(),
        notes: 'Manual stock adjustment',
        reason: reason ?? 'Stock quantity adjustment',
        createdBy: userId,
      );

      await loadStocks();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating stock quantity', e);
    }
  }

  // Transfer stock between warehouses
  Future<void> transferStock({
    required String productId,
    required String fromWarehouseId,
    required String toWarehouseId,
    required double quantity,
    required double unitCost,
    String? notes,
    String? reason,
    required String userId,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.transferStock(
        productId: productId,
        fromWarehouseId: fromWarehouseId,
        toWarehouseId: toWarehouseId,
        quantity: quantity,
        unitCost: unitCost,
        referenceType: 'manual_transfer',
        referenceId: AppUtils.generateId(),
        notes: notes ?? 'Manual stock transfer',
        reason: reason ?? 'Stock transfer between warehouses',
        createdBy: userId,
      );

      await loadStocks();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error transferring stock', e);
    }
  }
}

// Additional providers
final stockByIdProvider = FutureProvider.family<Stock?, String>((ref, id) async {
  final repository = ref.read(stockRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getStockById(id);
  });
});

final lowStockItemsProvider = FutureProvider<List<Stock>>((ref) async {
  final repository = ref.read(stockRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getLowStockItems();
  });
});

final expiredStockItemsProvider = FutureProvider<List<Stock>>((ref) async {
  final repository = ref.read(stockRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getExpiredStockItems();
  });
});

final nearExpiryStockItemsProvider = FutureProvider<List<Stock>>((ref) async {
  final repository = ref.read(stockRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getNearExpiryStockItems();
  });
});

final stockStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(stockRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getStockStatistics();
  });
});
