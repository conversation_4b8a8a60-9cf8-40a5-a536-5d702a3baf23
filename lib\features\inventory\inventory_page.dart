import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../core/utils/formatters.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../providers/stock_provider.dart';
import '../../providers/warehouse_provider.dart';

class InventoryPage extends ConsumerWidget {
  const InventoryPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'المخزون',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.productAdd),
        child: const Icon(Icons.add),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quick Stats
            _buildQuickStats(context, ref),
            SizedBox(height: 24.h),

            // Quick Actions
            _buildQuickActions(context),
            SizedBox(height: 24.h),

            // Recent Products
            _buildRecentProducts(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context, WidgetRef ref) {
    final productsState = ref.watch(enhancedProductManagementProvider);
    final stockState = ref.watch(stockManagementProvider);
    final warehousesAsync = ref.watch(activeWarehousesProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات المخزون',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          childAspectRatio: 1.0,
          children: [
            _buildStatCard(
              context,
              title: 'إجمالي المنتجات',
              value: '${productsState.products.length}',
              icon: Icons.inventory_2_outlined,
              color: AppColors.primary,
            ),
            _buildStatCard(
              context,
              title: 'مخزون منخفض',
              value: '${stockState.statistics['lowStockCount'] ?? 0}',
              icon: Icons.warning_amber_outlined,
              color: AppColors.warning,
            ),
            _buildStatCard(
              context,
              title: 'نفد المخزون',
              value: '${stockState.statistics['outOfStockCount'] ?? 0}',
              icon: Icons.error_outline,
              color: AppColors.error,
            ),
            _buildStatCard(
              context,
              title: 'قيمة المخزون',
              value: AppFormatters.formatCurrency(
                  stockState.statistics['totalValue'] ?? 0.0),
              icon: Icons.attach_money,
              color: AppColors.success,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          childAspectRatio: 0.9,
          children: [
            _buildActionCard(
              context,
              title: 'المنتجات',
              subtitle: 'عرض وإدارة المنتجات',
              icon: Icons.inventory_2_outlined,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.products),
            ),
            _buildActionCard(
              context,
              title: 'التصنيفات',
              subtitle: 'إدارة تصنيفات المنتجات',
              icon: Icons.category_outlined,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.categories),
            ),
            _buildActionCard(
              context,
              title: 'وحدات القياس',
              subtitle: 'إدارة وحدات القياس',
              icon: Icons.straighten_outlined,
              color: AppColors.accent,
              onTap: () => context.go(AppRoutes.units),
            ),
            _buildActionCard(
              context,
              title: 'المخازن',
              subtitle: 'إدارة المخازن والفروع',
              icon: Icons.warehouse_outlined,
              color: AppColors.info,
              onTap: () => context.go(AppRoutes.warehouses),
            ),
            _buildActionCard(
              context,
              title: 'سجل المخزون',
              subtitle: 'عرض حركات المخزون',
              icon: Icons.history_outlined,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.stockMovements),
            ),
            _buildActionCard(
              context,
              title: 'تعديل المخزون',
              subtitle: 'تعديل كميات المخزون',
              icon: Icons.edit_outlined,
              color: AppColors.warning,
              onTap: () => context.go(AppRoutes.stockAdjustment),
            ),
            _buildActionCard(
              context,
              title: 'تقارير المخزون',
              subtitle: 'تقارير وإحصائيات المخزون',
              icon: Icons.analytics_outlined,
              color: AppColors.accent,
              onTap: () {
                // TODO: إضافة مسار تقارير المخزون
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تقارير المخزون قيد التطوير')),
                );
              },
            ),
            _buildActionCard(
              context,
              title: 'تنبيهات المخزون',
              subtitle: 'تنبيهات نفاد المخزون',
              icon: Icons.notifications_outlined,
              color: AppColors.error,
              onTap: () {
                // TODO: إضافة مسار تنبيهات المخزون
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('تنبيهات المخزون قيد التطوير')),
                );
              },
            ),
            _buildActionCard(
              context,
              title: 'إضافة منتج',
              subtitle: 'إضافة منتج جديد',
              icon: Icons.add_box_outlined,
              color: AppColors.success,
              onTap: () => context.go(AppRoutes.productAdd),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32.r,
                color: color,
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentProducts(BuildContext context, WidgetRef ref) {
    final productsState = ref.watch(enhancedProductManagementProvider);
    final recentProducts = productsState.products.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المنتجات الأخيرة',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.go(AppRoutes.products),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Card(
          child: recentProducts.isEmpty
              ? Padding(
                  padding: EdgeInsets.all(32.r),
                  child: Column(
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: 48.r,
                        color: Colors.grey[400],
                      ),
                      SizedBox(height: 16.h),
                      Text(
                        'لا توجد منتجات',
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'ابدأ بإضافة منتجات جديدة',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: recentProducts.length,
                  separatorBuilder: (context, index) =>
                      const Divider(height: 1),
                  itemBuilder: (context, index) {
                    final product = recentProducts[index];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundColor:
                            AppColors.primary.withValues(alpha: 0.1),
                        child: Icon(
                          Icons.inventory_2_outlined,
                          color: AppColors.primary,
                          size: 20.r,
                        ),
                      ),
                      title: Text(
                        product.nameAr,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      subtitle: Text(
                        'السعر: ${AppFormatters.formatCurrency(product.sellingPrice)}',
                        style: AppTextStyles.bodySmall,
                      ),
                      trailing: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          color: product.isActive
                              ? AppColors.success
                              : AppColors.error,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Text(
                          product.isActive ? 'نشط' : 'غير نشط',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      onTap: () {
                        // TODO: Navigate to product details
                        context.go('${AppRoutes.products}/${product.id}');
                      },
                    );
                  },
                ),
        ),
      ],
    );
  }
}
