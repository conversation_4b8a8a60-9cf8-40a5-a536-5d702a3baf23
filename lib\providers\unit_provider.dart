import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tijari_tech/data/models/unit.dart';
import 'package:tijari_tech/features/units/add_unit_dialog.dart';

import '../data/repositories/unit_repository.dart';
import '../core/utils/app_utils.dart';
import '../core/utils/error_handler.dart';
import 'database_provider.dart';

// Unit repository provider
final enhancedUnitRepositoryProvider = Provider<UnitRepository>((ref) {
  return UnitRepository();
});

// Enhanced Unit management provider
final enhancedUnitManagementProvider = StateNotifierProvider<
    EnhancedUnitManagementNotifier, EnhancedUnitManagementState>((ref) {
  return EnhancedUnitManagementNotifier(ref);
});

// Enhanced Unit management state
class EnhancedUnitManagementState {
  final bool isLoading;
  final String? error;
  final List<Unit> units;
  final List<Unit> filteredUnits;
  final String searchQuery;
  final String? selectedUnitType;
  final bool showInactive;
  final Unit? selectedUnit;
  final Map<String, dynamic> statistics;

  const EnhancedUnitManagementState({
    this.isLoading = false,
    this.error,
    this.units = const [],
    this.filteredUnits = const [],
    this.searchQuery = '',
    this.selectedUnitType,
    this.showInactive = false,
    this.selectedUnit,
    this.statistics = const {},
  });

  EnhancedUnitManagementState copyWith({
    bool? isLoading,
    String? error,
    List<Unit>? units,
    List<Unit>? filteredUnits,
    String? searchQuery,
    String? selectedUnitType,
    bool? showInactive,
    Unit? selectedUnit,
    Map<String, dynamic>? statistics,
  }) {
    return EnhancedUnitManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      units: units ?? this.units,
      filteredUnits: filteredUnits ?? this.filteredUnits,
      searchQuery: searchQuery ?? this.searchQuery,
      selectedUnitType: selectedUnitType ?? this.selectedUnitType,
      showInactive: showInactive ?? this.showInactive,
      selectedUnit: selectedUnit ?? this.selectedUnit,
      statistics: statistics ?? this.statistics,
    );
  }
}

// Enhanced Unit management notifier
class EnhancedUnitManagementNotifier
    extends StateNotifier<EnhancedUnitManagementState> {
  final Ref _ref;

  EnhancedUnitManagementNotifier(this._ref)
      : super(const EnhancedUnitManagementState()) {
    _loadUnits();
  }

  UnitRepository get _repository => _ref.read(enhancedUnitRepositoryProvider);
  DatabaseOperations get _dbOperations => _ref.read(databaseOperationsProvider);

  // Load all units
  Future<void> _loadUnits() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final units = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getActiveUnits();
      });

      final statistics = await _dbOperations.executeWithReadyCheck(() async {
        return await _repository.getUnitsStatistics();
      });

      state = state.copyWith(
        isLoading: false,
        units: units,
        filteredUnits: _filterUnits(units),
        statistics: statistics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error loading units', e);
    }
  }

  // Filter units based on search query, type, and show inactive setting
  List<Unit> _filterUnits(List<Unit> units) {
    var filtered = units;

    // Filter by search query
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered.where((unit) {
        return unit.name.toLowerCase().contains(query) ||
            (unit.symbol?.toLowerCase().contains(query) ?? false) ||
            (unit.abbreviation?.toLowerCase().contains(query) ?? false) ||
            (unit.description?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Filter by unit type
    if (state.selectedUnitType != null) {
      filtered = filtered
          .where((unit) => unit.unitType == state.selectedUnitType)
          .toList();
    }

    // Filter by active status
    if (!state.showInactive) {
      filtered = filtered.where((unit) => unit.isActiveUnit).toList();
    }

    return filtered;
  }

  // Search units
  void searchUnits(String query) {
    state = state.copyWith(
      searchQuery: query,
      filteredUnits: _filterUnits(state.units),
    );
  }

  // Filter by unit type
  void filterByUnitType(String? unitType) {
    state = state.copyWith(
      selectedUnitType: unitType,
      filteredUnits: _filterUnits(state.units),
    );
  }

  // Toggle show inactive units
  void toggleShowInactive(bool showInactive) {
    state = state.copyWith(
      showInactive: showInactive,
      filteredUnits: _filterUnits(state.units),
    );
  }

  // Select unit
  void selectUnit(Unit? unit) {
    state = state.copyWith(selectedUnit: unit);
  }

  // Refresh units
  Future<void> refresh() async {
    await _loadUnits();
    _ref.invalidate(enhancedUnitRepositoryProvider);
  }

  Future<bool> createUnit({
    required String nameAr, // اسم عربي
    // String? nameEn, // اسم انجليزي
    required String symbol,
    required UnitType type, // استخدام enum مباشرة
    String? baseUnitId,
    double? conversionFactor, // معامل التحويل
    bool isActive = true,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final unit = Unit.create(
        id: AppUtils.generateId(),
        name: nameAr,
        // nameEn: nameEn,
        symbol: symbol,
        factor: conversionFactor ?? 1.0,
        baseUnitId: baseUnitId,
        unitType: type.toString().split('.').last,
        isBaseUnit: baseUnitId == null,
        isActive: isActive,
      );

      await _repository.createUnit(unit);
      await refresh();
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      return false;
    }
  }

  // Update unit
  Future<bool> updateUnit({
    required String id,
    String? name,
    String? symbol,
    String? abbreviation,
    double? factor,
    String? baseUnitId,
    String? unitType,
    bool? isBaseUnit,
    bool? isActive,
    String? description,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final existingUnit = await _repository.getUnitWithDetails(id);
      if (existingUnit == null) {
        throw Exception('Unit not found');
      }

      final updatedUnit = existingUnit.update(
        name: name,
        symbol: symbol,
        abbreviation: abbreviation,
        factor: factor,
        baseUnitId: baseUnitId,
        unitType: unitType,
        isBaseUnit: isBaseUnit,
        isActive: isActive,
        description: description,
      );

      await _repository.updateUnit(id, updatedUnit);
      await refresh();
      AppUtils.logInfo('Unit updated successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error updating unit', e);
      return false;
    }
  }

  // Delete unit
  Future<bool> deleteUnit(String id) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _repository.deleteUnit(id);
      await refresh();
      AppUtils.logInfo('Unit deleted successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error deleting unit', e);
      return false;
    }
  }

  // Convert quantity between units
  Future<double?> convertQuantity({
    required double quantity,
    required String fromUnitId,
    required String toUnitId,
  }) async {
    try {
      return await _repository.convertQuantity(
        quantity: quantity,
        fromUnitId: fromUnitId,
        toUnitId: toUnitId,
      );
    } catch (e) {
      AppUtils.logError('Error converting quantity', e);
      return null;
    }
  }

  // Get conversion rate between units
  Future<double?> getConversionRate({
    required String fromUnitId,
    required String toUnitId,
  }) async {
    try {
      final fromUnit = await _repository.getUnitById(fromUnitId);
      final toUnit = await _repository.getUnitById(toUnitId);

      if (fromUnit == null || toUnit == null) return null;

      return fromUnit.getConversionFactor(toUnit);
    } catch (e) {
      AppUtils.logError('Error getting conversion rate', e);
      return null;
    }
  }

  // Create default units
  Future<bool> createDefaultUnits() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Create some default units
      final defaultUnits = [
        Unit.create(id: AppUtils.generateId(), name: 'قطعة', factor: 1.0),
        Unit.create(
            id: AppUtils.generateId(), name: 'كيلوجرام', factor: 1000.0),
        Unit.create(id: AppUtils.generateId(), name: 'جرام', factor: 1.0),
        Unit.create(id: AppUtils.generateId(), name: 'لتر', factor: 1000.0),
      ];

      for (final unit in defaultUnits) {
        await _repository.createUnit(unit);
      }

      await refresh();
      AppUtils.logInfo('Default units created successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error creating default units', e);
      return false;
    }
  }

  // Validate data integrity
  Future<List<Map<String, dynamic>>> validateDataIntegrity() async {
    try {
      // For now, return empty list as this functionality needs to be implemented
      return [];
    } catch (e) {
      AppUtils.logError('Error validating data integrity', e);
      return [];
    }
  }

  // Fix data integrity issues
  Future<bool> fixDataIntegrityIssues() async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // For now, return true as this functionality needs to be implemented
      await refresh();
      AppUtils.logInfo('Data integrity issues fixed successfully');
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: ErrorHandler.handleError(e),
      );
      AppUtils.logError('Error fixing data integrity issues', e);
      return false;
    }
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Unit by ID provider
final enhancedUnitByIdProvider =
    FutureProvider.family<Unit?, String>((ref, id) async {
  final repository = ref.read(enhancedUnitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getUnitWithDetails(id);
  });
});

// Base units provider
final baseUnitsProvider = FutureProvider<List<Unit>>((ref) async {
  final repository = ref.read(enhancedUnitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getBaseUnits();
  });
});

// Unit types provider
final unitTypesProvider = FutureProvider<List<String>>((ref) async {
  final repository = ref.read(enhancedUnitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getUnitTypes();
  });
});

// Units statistics provider
final unitsStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.read(enhancedUnitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getUnitsStatistics();
  });
});

// Most used units provider
final mostUsedUnitsProvider = FutureProvider<List<Unit>>((ref) async {
  final repository = ref.read(enhancedUnitRepositoryProvider);
  final dbOperations = ref.read(databaseOperationsProvider);

  return await dbOperations.executeWithReadyCheck(() async {
    return await repository.getMostUsedUnits();
  });
});
