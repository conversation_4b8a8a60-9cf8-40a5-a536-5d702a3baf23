import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';
import '../../widgets/dashboard_widgets.dart';

class DashboardPage extends ConsumerWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: "لوحة التحكم",
      child: ResponsiveWrapper(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome Section
              _buildWelcomeSection(context),
              SizedBox(height: 24.h),

              // Quick Stats
              _buildQuickStats(context),
              SizedBox(height: 24.h),
              // _buildChartsSection(context),
              // Quick Actions
              _buildQuickActions(context),
              SizedBox(height: 24.h),

              // Recent Activities
              _buildRecentActivities(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مرحباً بك',
            style: AppTextStyles.headlineMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'نظام إدارة شامل لأعمالك التجارية',
            style: AppTextStyles.bodyLarge.copyWith(
              color: Colors.white70,
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            DateTime.now().toDisplayDate,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.white60,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: context.isMobile ? 2 : 4,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
          childAspectRatio: .8,
          children: [
            _buildStatCard(
              context,
              title: 'مبيعات اليوم',
              value: '12,500 ر.س',
              icon: Icons.trending_up,
              color: AppColors.success,
            ),
            _buildStatCard(
              context,
              title: 'إجمالي العملاء',
              value: '245',
              icon: Icons.people_outline,
              color: AppColors.info,
            ),
            _buildStatCard(
              context,
              title: 'المنتجات',
              value: '1,234',
              icon: Icons.inventory_2_outlined,
              color: AppColors.warning,
            ),
            _buildStatCard(
              context,
              title: 'مخزون منخفض',
              value: '23',
              icon: Icons.warning_amber_outlined,
              color: AppColors.error,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: context.isMobile ? 2 : 3,
          crossAxisSpacing: 16.w,
          mainAxisSpacing: 16.h,
          childAspectRatio: 1.3,
          children: [
            _buildActionCard(
              context,
              title: 'نقطة البيع',
              icon: Icons.point_of_sale,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.pos),
            ),
            _buildActionCard(
              context,
              title: 'إضافة منتج',
              icon: Icons.add_box_outlined,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.productAdd),
            ),
            _buildActionCard(
              context,
              title: 'فاتورة شراء',
              icon: Icons.receipt_long_outlined,
              color: AppColors.accent,
              onTap: () => context.go(AppRoutes.purchaseAdd),
            ),
            _buildActionCard(
              context,
              title: 'إضافة عميل',
              icon: Icons.person_add_outlined,
              color: AppColors.info,
              onTap: () => context.go(AppRoutes.customerAdd),
            ),
            _buildActionCard(
              context,
              title: 'التقارير',
              icon: Icons.analytics_outlined,
              color: AppColors.warning,
              onTap: () => context.go(AppRoutes.reports),
            ),
            _buildActionCard(
              context,
              title: 'المزامنة',
              icon: Icons.sync_outlined,
              color: AppColors.success,
              onTap: () {
                // TODO: Implement sync
                context.showInfoSnackBar('جاري المزامنة...');
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32.r,
                color: color,
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChartsSection(BuildContext context) {
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الرسوم البيانية',
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SalesChart(
            title: 'المبيعات الشهرية',
            data: _getSampleSalesData(),
          ),
          SizedBox(height: 16.h),
          CategoryPieChart(
            title: 'المبيعات حسب الفئة',
            data: _getSampleCategoryData(),
          ),
        ],
      ),
    );
  }

  List<SalesData> _getSampleSalesData() {
    return [
      SalesData(label: 'يناير', value: 15000, color: AppColors.primary),
      SalesData(label: 'فبراير', value: 18000, color: AppColors.primary),
      SalesData(label: 'مارس', value: 22000, color: AppColors.primary),
      SalesData(label: 'أبريل', value: 19000, color: AppColors.primary),
      SalesData(label: 'مايو', value: 25000, color: AppColors.primary),
      SalesData(label: 'يونيو', value: 28000, color: AppColors.primary),
    ];
  }

  List<CategoryData> _getSampleCategoryData() {
    return [
      CategoryData(
        label: 'إلكترونيات',
        value: 35,
        percentage: 35,
        color: AppColors.primary,
      ),
      CategoryData(
        label: 'ملابس',
        value: 25,
        percentage: 25,
        color: AppColors.success,
      ),
      CategoryData(
        label: 'أغذية',
        value: 20,
        percentage: 20,
        color: AppColors.warning,
      ),
      CategoryData(
        label: 'أخرى',
        value: 20,
        percentage: 20,
        color: AppColors.info,
      ),
    ];
  }

  Widget _buildRecentActivities(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'النشاطات الأخيرة',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to full activity log
              },
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.receipt_outlined,
                    color: AppColors.primary,
                    size: 20.r,
                  ),
                ),
                title: Text(
                  'فاتورة مبيعات #INV00012${index + 1}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  'عميل: أحمد محمد - ${(1250.0 * (index + 1)).toCurrency}',
                  style: AppTextStyles.bodySmall,
                ),
                trailing: Text(
                  '${index + 1} دقيقة',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                onTap: () {
                  // TODO: Navigate to invoice details
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
