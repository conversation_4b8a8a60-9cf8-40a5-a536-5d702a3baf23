// استيراد المكتبات المطلوبة
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

// استيراد ملفات التطبيق
import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unit_provider.dart';
import '../../data/models/product_image.dart';
// import '../../data/models/enhanced_product.dart'; // TODO: إضافة نموذج المنتج المحسن
import '../categories/add_edite_category_dialog.dart';
import '../units/add_unit_dialog.dart';
import 'barcode_scanner_widget.dart';

/// صفحة إضافة/تعديل منتج
/// تدعم إضافة منتج جديد أو تعديل منتج موجود
/// مع إمكانية إضافة الصور ومسح الباركود وتحديد تاريخ الانتهاء
class AddEditProductPage extends ConsumerStatefulWidget {
  final bool isEdit; // هل هي عملية تعديل أم إضافة
  final String? productId; // معرف المنتج للتعديل

  const AddEditProductPage({
    super.key,
    this.isEdit = false,
    this.productId,
  });

  @override
  ConsumerState<AddEditProductPage> createState() => _AddEditProductPageState();
}

class _AddEditProductPageState extends ConsumerState<AddEditProductPage> {
  // مفتاح النموذج للتحقق من صحة البيانات
  final _formKey = GlobalKey<FormState>();

  // متحكمات النصوص للحقول المختلفة
  final _nameArController = TextEditingController(); // اسم المنتج بالعربية
  final _nameEnController = TextEditingController(); // اسم المنتج بالإنجليزية
  final _barcodeController = TextEditingController(); // الباركود
  final _skuController = TextEditingController(); // رمز المنتج
  final _descriptionController = TextEditingController(); // وصف المنتج
  final _costPriceController = TextEditingController(); // سعر التكلفة
  final _sellingPriceController = TextEditingController(); // سعر البيع
  final _minStockController = TextEditingController(); // الحد الأدنى للمخزون
  final _maxStockController = TextEditingController(); // الحد الأقصى للمخزون

  // متغيرات الاختيار
  String? _selectedCategoryId; // معرف التصنيف المختار
  String? _selectedUnitId; // معرف وحدة القياس المختارة
  bool _isActive = true; // حالة نشاط المنتج
  bool _trackStock = true; // تتبع المخزون
  bool _isLoading = false; // حالة التحميل
  DateTime? _expiryDate; // تاريخ انتهاء المنتج

  // متغيرات إدارة الصور والباركود
  final List<ProductImage> _productImages = []; // قائمة صور المنتج
  // EnhancedProduct? _currentProduct; // المنتج الحالي للتعديل - TODO: إضافة نموذج المنتج

  /// تنظيف الموارد عند إغلاق الصفحة
  @override
  void dispose() {
    // تنظيف جميع متحكمات النصوص لتجنب تسريب الذاكرة
    _nameArController.dispose();
    _nameEnController.dispose();
    _barcodeController.dispose();
    _skuController.dispose();
    _descriptionController.dispose();
    _costPriceController.dispose();
    _sellingPriceController.dispose();
    _minStockController.dispose();
    _maxStockController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    // إذا كان تعديل، تحميل بيانات المنتج
    if (widget.isEdit && widget.productId != null) {
      _loadProduct();
    }
  }

  /// تحميل بيانات المنتج للتعديل
  Future<void> _loadProduct() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final product = await ref
          .read(enhancedProductManagementProvider.notifier)
          .getProductById(widget.productId!);

      if (product != null && mounted) {
        setState(() {
          _nameArController.text = product.nameAr;
          _nameEnController.text = product.nameEn ?? '';
          _barcodeController.text = product.barcode ?? '';
          _skuController.text = product.sku ?? '';
          _descriptionController.text = product.description ?? '';
          _costPriceController.text = product.costPrice.toString();
          _sellingPriceController.text = product.sellingPrice.toString();
          _minStockController.text = product.minStock.toString();
          _maxStockController.text = product.maxStock.toString();
          _selectedCategoryId = product.categoryId;
          _selectedUnitId = product.baseUnitId;
          _isActive = product.isActive;
          _trackStock = product.trackStock;
          // _expiryDate = product.expiryDate; // TODO: إضافة دعم تاريخ الانتهاء في نموذج المنتج
          _isLoading = false;
        });
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لم يتم العثور على المنتج'),
              backgroundColor: Colors.red,
            ),
          );
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات المنتج: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// بناء واجهة المستخدم الرئيسية
  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: widget.isEdit ? 'تعديل المنتج' : 'إضافة منتج جديد',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // قسم المعلومات الأساسية - اسم المنتج، الباركود، التصنيف، وحدة القياس
              _buildBasicInfoCard(),
              SizedBox(height: 16.h),

              // قسم صور المنتج - إضافة وإدارة صور المنتج
              _buildProductImagesCard(),
              SizedBox(height: 16.h),

              // قسم معلومات التسعير - سعر التكلفة وسعر البيع
              _buildPricingInfoCard(),
              SizedBox(height: 16.h),

              // قسم معلومات المخزون - تتبع المخزون والحدود
              _buildStockInfoCard(),
              SizedBox(height: 16.h),

              // قسم تاريخ الانتهاء والمعلومات الإضافية
              _buildAdditionalInfoCard(),
              SizedBox(height: 24.h),

              // أزرار الإجراءات - حفظ وإلغاء
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات الأساسية للمنتج
  /// تحتوي على: اسم المنتج، الباركود، رمز المنتج، التصنيف، وحدة القياس
  Widget _buildBasicInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Text(
              'المعلومات الأساسية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            // حقل اسم المنتج بالعربية (مطلوب)
            TextFormField(
              controller: _nameArController,
              decoration: const InputDecoration(
                labelText: 'اسم المنتج (عربي) *',
                hintText: 'أدخل اسم المنتج بالعربية',
                prefixIcon: Icon(Icons.inventory_outlined),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم المنتج';
                }
                return null;
              },
            ),
            SizedBox(height: 16.h),

            // حقل اسم المنتج بالإنجليزية (اختياري)
            TextFormField(
              controller: _nameEnController,
              decoration: const InputDecoration(
                labelText: 'اسم المنتج (إنجليزي)',
                hintText: 'أدخل اسم المنتج بالإنجليزية',
                prefixIcon: Icon(Icons.translate_outlined),
              ),
            ),
            SizedBox(height: 16.h),

            // حقل رمز المنتج (SKU) - يتم توليده تلقائياً إذا ترك فارغاً
            TextFormField(
              controller: _skuController,
              decoration: const InputDecoration(
                labelText: 'رمز المنتج (SKU)',
                hintText: 'أدخل رمز المنتج أو اتركه فارغاً للتوليد التلقائي',
                prefixIcon: Icon(Icons.tag_outlined),
              ),
            ),
            SizedBox(height: 16.h),

            // حقل الباركود مع إمكانية المسح والتوليد التلقائي
            TextFormField(
              controller: _barcodeController,
              decoration: InputDecoration(
                labelText: 'الباركود',
                hintText: 'أدخل الباركود أو اتركه فارغاً للتوليد التلقائي',
                prefixIcon: const Icon(Icons.qr_code_outlined),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر مسح الباركود باستخدام الكاميرا
                    IconButton(
                      icon: const Icon(Icons.qr_code_scanner),
                      onPressed: _openBarcodeScanner,
                      tooltip: 'مسح الباركود',
                    ),
                    // زر توليد باركود عشوائي
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      onPressed: _generateRandomBarcode,
                      tooltip: 'توليد باركود عشوائي',
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16.h),

            // قسم اختيار التصنيف مع إمكانية إضافة تصنيف جديد
            Row(
              children: [
                Expanded(
                  child: Consumer(
                    builder: (context, ref, child) {
                      final categoriesAsync = ref.watch(mainCategoriesProvider);
                      return categoriesAsync.when(
                        // عرض قائمة التصنيفات المتاحة
                        data: (categories) => DropdownButtonFormField<String>(
                          value: _selectedCategoryId,
                          decoration: const InputDecoration(
                            labelText: 'التصنيف *',
                            prefixIcon: Icon(Icons.category_outlined),
                          ),
                          items: categories.map((category) {
                            return DropdownMenuItem<String>(
                              value: category.id,
                              child: Text(category.nameAr),
                            );
                          }).toList(),
                          onChanged: (String? newValue) {
                            setState(() {
                              _selectedCategoryId = newValue;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'يرجى اختيار التصنيف';
                            }
                            return null;
                          },
                        ),
                        loading: () => const CircularProgressIndicator(),
                        error: (error, stack) =>
                            const Text('خطأ في تحميل التصنيفات'),
                      );
                    },
                  ),
                ),
                SizedBox(width: 8.w),
                // زر إضافة تصنيف جديد
                IconButton(
                  onPressed: () => _showAddCategoryDialog(),
                  icon: const Icon(Icons.add_circle_outline),
                  tooltip: 'إضافة تصنيف جديد',
                  color: AppColors.primary,
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // قسم اختيار وحدة القياس مع إمكانية إضافة وحدة جديدة
            Row(
              children: [
                Expanded(
                  child: Consumer(
                    builder: (context, ref, child) {
                      final unitsState =
                          ref.watch(enhancedUnitManagementProvider);
                      return DropdownButtonFormField<String>(
                        value: _selectedUnitId,
                        decoration: const InputDecoration(
                          labelText: 'وحدة القياس *',
                          prefixIcon: Icon(Icons.straighten_outlined),
                        ),
                        // عرض قائمة وحدات القياس المتاحة
                        items: unitsState.units.map((unit) {
                          return DropdownMenuItem<String>(
                            value: unit.id,
                            child: Text(unit.displayName),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          setState(() {
                            _selectedUnitId = newValue;
                          });
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'يرجى اختيار وحدة القياس';
                          }
                          return null;
                        },
                      );
                    },
                  ),
                ),
                SizedBox(width: 8.w),
                // زر إضافة وحدة قياس جديدة
                IconButton(
                  onPressed: () => _showAddUnitDialog(),
                  icon: const Icon(Icons.add_circle_outline),
                  tooltip: 'إضافة وحدة قياس جديدة',
                  color: AppColors.primary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم صور المنتج
  Widget _buildProductImagesCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'صور المنتج',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _showImagePickerDialog,
                  icon: const Icon(Icons.add_photo_alternate),
                  label: const Text('إضافة صورة'),
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // عرض الصور أو حالة فارغة
            _productImages.isEmpty
                ? _buildEmptyImagesState()
                : _buildImagesGrid(),
          ],
        ),
      ),
    );
  }

  /// حالة عدم وجود صور
  Widget _buildEmptyImagesState() {
    return Container(
      height: 120.h,
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey[300]!,
          style: BorderStyle.solid,
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_outlined,
            size: 48.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 8.h),
          Text(
            'لا توجد صور',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'اضغط "إضافة صورة" لإضافة صور المنتج',
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// شبكة عرض الصور
  Widget _buildImagesGrid() {
    return SizedBox(
      height: 120.h,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _productImages.length,
        itemBuilder: (context, index) {
          final image = _productImages[index];
          return Container(
            width: 100.w,
            margin: EdgeInsets.only(right: 8.w),
            child: Stack(
              children: [
                // الصورة
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: Container(
                    width: 100.w,
                    height: 100.h,
                    color: Colors.grey[200],
                    child: image.imagePath != null
                        ? Image.asset(
                            'assets/images/default_product.png',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                Icon(Icons.image, size: 40.r),
                          )
                        : Icon(Icons.image, size: 40.r),
                  ),
                ),

                // زر الحذف
                Positioned(
                  top: 4.r,
                  right: 4.r,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      padding: EdgeInsets.all(4.r),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16.r,
                      ),
                    ),
                  ),
                ),

                // مؤشر الصورة الرئيسية
                if (image.isPrimary)
                  Positioned(
                    bottom: 4.r,
                    left: 4.r,
                    child: Container(
                      padding: EdgeInsets.all(2.r),
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Icon(
                        Icons.star,
                        color: Colors.white,
                        size: 12.r,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// بناء قسم معلومات التسعير
  Widget _buildPricingInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معلومات التسعير',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            Row(
              children: [
                // سعر التكلفة
                Expanded(
                  child: TextFormField(
                    controller: _costPriceController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'سعر التكلفة *',
                      hintText: '0.00',
                      prefixIcon: Icon(Icons.attach_money_outlined),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال سعر التكلفة';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                    onChanged: (value) => setState(() {}),
                  ),
                ),
                SizedBox(width: 16.w),

                // سعر البيع
                Expanded(
                  child: TextFormField(
                    controller: _sellingPriceController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'سعر البيع *',
                      hintText: '0.00',
                      prefixIcon: Icon(Icons.sell_outlined),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال سعر البيع';
                      }
                      if (double.tryParse(value) == null) {
                        return 'يرجى إدخال رقم صحيح';
                      }
                      return null;
                    },
                    onChanged: (value) => setState(() {}),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16.h),

            // عرض هامش الربح
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: AppColors.info.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: AppColors.info.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.trending_up,
                    color: AppColors.info,
                    size: 20.r,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'هامش الربح: ${_calculateProfitMargin()}%',
                    style: AppTextStyles.bodyMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppColors.info,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم معلومات المخزون
  Widget _buildStockInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'معلومات المخزون',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.inventory_2_outlined,
                  color: AppColors.primary,
                  size: 24.r,
                ),
              ],
            ),
            SizedBox(height: 16.h),

            // مفتاح تتبع المخزون
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: _trackStock
                    ? AppColors.success.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: _trackStock
                      ? AppColors.success.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              child: SwitchListTile(
                title: Text(
                  'تتبع المخزون',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  _trackStock
                      ? 'سيتم تتبع كمية المخزون وإرسال تنبيهات عند النفاد'
                      : 'لن يتم تتبع كمية المخزون لهذا المنتج',
                  style: AppTextStyles.bodySmall,
                ),
                value: _trackStock,
                onChanged: (bool value) {
                  setState(() {
                    _trackStock = value;
                    if (!value) {
                      _minStockController.clear();
                      _maxStockController.clear();
                    }
                  });
                },
                activeColor: AppColors.success,
                contentPadding: EdgeInsets.zero,
              ),
            ),

            // حقول المخزون المتقدمة
            if (_trackStock) ...[
              SizedBox(height: 20.h),
              Text(
                'حدود المخزون',
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
              SizedBox(height: 12.h),
              Row(
                children: [
                  // الحد الأدنى
                  Expanded(
                    child: TextFormField(
                      controller: _minStockController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'الحد الأدنى للمخزون *',
                        hintText: '0',
                        prefixIcon: Icon(
                          Icons.warning_outlined,
                          color: AppColors.warning,
                        ),
                        helperText: 'تنبيه عند الوصول لهذا الحد',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      validator: (value) {
                        if (_trackStock) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال الحد الأدنى';
                          }
                          final minValue = double.tryParse(value);
                          if (minValue == null || minValue < 0) {
                            return 'يرجى إدخال رقم صحيح أكبر من أو يساوي 0';
                          }

                          final maxValue =
                              double.tryParse(_maxStockController.text);
                          if (maxValue != null && minValue >= maxValue) {
                            return 'يجب أن يكون أقل من الحد الأقصى';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 16.w),

                  // الحد الأقصى
                  Expanded(
                    child: TextFormField(
                      controller: _maxStockController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'الحد الأقصى للمخزون',
                        hintText: '0',
                        prefixIcon: Icon(
                          Icons.inventory_2_outlined,
                          color: AppColors.info,
                        ),
                        helperText: 'الحد الأقصى المسموح (اختياري)',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final maxValue = double.tryParse(value);
                          if (maxValue == null || maxValue <= 0) {
                            return 'يرجى إدخال رقم صحيح أكبر من 0';
                          }

                          final minValue =
                              double.tryParse(_minStockController.text);
                          if (minValue != null && maxValue <= minValue) {
                            return 'يجب أن يكون أكبر من الحد الأدنى';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء قسم المعلومات الإضافية وتاريخ الانتهاء
  Widget _buildAdditionalInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعلومات الإضافية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            // وصف المنتج
            TextFormField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'وصف المنتج',
                hintText: 'أدخل وصف تفصيلي للمنتج',
                prefixIcon: Icon(Icons.description_outlined),
                alignLabelWithHint: true,
              ),
            ),
            SizedBox(height: 16.h),

            // تاريخ انتهاء المنتج
            InkWell(
              onTap: _selectExpiryDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'تاريخ انتهاء المنتج',
                  hintText: 'اختر تاريخ انتهاء المنتج (اختياري)',
                  prefixIcon: Icon(Icons.calendar_today_outlined),
                  suffixIcon: Icon(Icons.arrow_drop_down),
                ),
                child: Text(
                  _expiryDate != null
                      ? DateFormat('yyyy/MM/dd').format(_expiryDate!)
                      : 'لا يوجد تاريخ انتهاء',
                  style: _expiryDate != null
                      ? AppTextStyles.bodyMedium
                      : AppTextStyles.bodyMedium.copyWith(
                          color: Colors.grey[600],
                        ),
                ),
              ),
            ),
            SizedBox(height: 16.h),

            // حالة نشاط المنتج
            Container(
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: _isActive
                    ? AppColors.success.withValues(alpha: 0.1)
                    : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: _isActive
                      ? AppColors.success.withValues(alpha: 0.3)
                      : Colors.grey.withValues(alpha: 0.3),
                ),
              ),
              child: SwitchListTile(
                title: Text(
                  'المنتج نشط',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  _isActive
                      ? 'المنتج متاح للبيع والعرض'
                      : 'المنتج غير متاح للبيع',
                  style: AppTextStyles.bodySmall,
                ),
                value: _isActive,
                onChanged: (bool value) {
                  setState(() {
                    _isActive = value;
                  });
                },
                activeColor: AppColors.success,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons() {
    return Row(
      children: [
        // زر الإلغاء
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: Text(
              'إلغاء',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        SizedBox(width: 16.w),

        // زر الحفظ
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveProduct,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: 16.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: _isLoading
                ? SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: const CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    widget.isEdit ? 'تحديث المنتج' : 'حفظ المنتج',
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  /// حساب هامش الربح بناءً على سعر التكلفة وسعر البيع
  String _calculateProfitMargin() {
    final costPrice = double.tryParse(_costPriceController.text) ?? 0;
    final sellingPrice = double.tryParse(_sellingPriceController.text) ?? 0;

    if (costPrice == 0) return '0.0';

    final margin = ((sellingPrice - costPrice) / costPrice) * 100;
    return margin.toStringAsFixed(1);
  }

  /// اختيار تاريخ انتهاء المنتج
  Future<void> _selectExpiryDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _expiryDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 سنوات
      helpText: 'اختر تاريخ انتهاء المنتج',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _expiryDate) {
      setState(() {
        _expiryDate = picked;
      });
    }
  }

  /// عرض حوار اختيار الصورة
  void _showImagePickerDialog() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إضافة صورة للمنتج',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16.h),
              ListTile(
                leading: const Icon(Icons.camera_alt, color: AppColors.primary),
                title: const Text('التقاط صورة'),
                subtitle: const Text('استخدام الكاميرا لالتقاط صورة جديدة'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera();
                },
              ),
              ListTile(
                leading:
                    const Icon(Icons.photo_library, color: AppColors.primary),
                title: const Text('اختيار من المعرض'),
                subtitle: const Text('اختيار صورة من معرض الصور'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),
              ListTile(
                leading: const Icon(Icons.image, color: AppColors.primary),
                title: const Text('استخدام صورة افتراضية'),
                subtitle: const Text('استخدام صورة افتراضية للمنتج'),
                onTap: () {
                  Navigator.pop(context);
                  _useDefaultImage();
                },
              ),
              SizedBox(height: 8.h),
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// التقاط صورة من الكاميرا
  void _pickImageFromCamera() {
    _addDummyImage('camera');
  }

  /// اختيار صورة من المعرض
  void _pickImageFromGallery() {
    _addDummyImage('gallery');
  }

  /// استخدام صورة افتراضية
  void _useDefaultImage() {
    _addDummyImage('default');
  }

  /// إضافة صورة وهمية للاختبار
  void _addDummyImage(String source) {
    final newImage = ProductImage.create(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      productId: widget.productId ?? 'temp',
      imageUrl: '',
      imagePath: 'assets/images/default_product.png',
      isPrimary: _productImages.isEmpty,
      sortOrder: _productImages.length,
      description: 'صورة من $source',
    );

    setState(() {
      _productImages.add(newImage);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة الصورة من $source'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  /// حذف صورة
  void _removeImage(int index) {
    setState(() {
      final removedImage = _productImages.removeAt(index);

      if (removedImage.isPrimary && _productImages.isNotEmpty) {
        _productImages[0] = _productImages[0].copyWith(isPrimary: true);
      }
    });
  }

  /// عرض ماسح الباركود
  void _openBarcodeScanner() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          height: 500.h,
          padding: EdgeInsets.all(16.r),
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    'مسح الباركود',
                    style: AppTextStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Expanded(
                child: BarcodeScannerWidget(
                  onBarcodeScanned: (barcode) {
                    setState(() {
                      _barcodeController.text = barcode;
                    });
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('تم مسح الباركود: $barcode'),
                        backgroundColor: AppColors.success,
                      ),
                    );
                  },
                  showProductInfo: true,
                  allowManualEntry: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// توليد باركود عشوائي
  void _generateRandomBarcode() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomBarcode = timestamp.toString().substring(5);

    setState(() {
      _barcodeController.text = randomBarcode;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم توليد باركود: $randomBarcode'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  /// عرض حوار إضافة تصنيف جديد
  void _showAddCategoryDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddEditedCategoryDialog(),
    );
  }

  /// عرض حوار إضافة وحدة قياس جديدة
  void _showAddUnitDialog() {
    showDialog(
      context: context,
      builder: (context) => const AddUnitDialog(),
    );
  }

  /// حفظ المنتج
  Future<void> _saveProduct() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        if (widget.isEdit) {
          // تحديث المنتج الموجود
          final result = await ref
              .read(enhancedProductManagementProvider.notifier)
              .updateProduct(
                id: widget.productId!,
                nameAr: _nameArController.text.trim(),
                nameEn: _nameEnController.text.trim().isEmpty
                    ? null
                    : _nameEnController.text.trim(),
                barcode: _barcodeController.text.trim().isEmpty
                    ? null
                    : _barcodeController.text.trim(),
                sku: _skuController.text.trim().isEmpty
                    ? null
                    : _skuController.text.trim(),
                description: _descriptionController.text.trim().isEmpty
                    ? null
                    : _descriptionController.text.trim(),
                baseUnitId: _selectedUnitId,
                categoryId: _selectedCategoryId,
                costPrice: double.parse(_costPriceController.text),
                sellingPrice: double.parse(_sellingPriceController.text),
                minStock: _trackStock && _minStockController.text.isNotEmpty
                    ? double.parse(_minStockController.text)
                    : 0.0,
                maxStock: _trackStock && _maxStockController.text.isNotEmpty
                    ? double.parse(_maxStockController.text)
                    : 0.0,
                isActive: _isActive,
                trackStock: _trackStock,
                expiryDate: _expiryDate,
              );

          if (mounted) {
            if (result) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('تم تحديث المنتج "${_nameArController.text}" بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
              Navigator.of(context).pop();
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('فشل في تحديث المنتج'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } else {
          // إضافة منتج جديد
          final result = await ref
              .read(enhancedProductManagementProvider.notifier)
              .createProduct(
                nameAr: _nameArController.text.trim(),
                nameEn: _nameEnController.text.trim().isEmpty
                    ? null
                    : _nameEnController.text.trim(),
                barcode: _barcodeController.text.trim().isEmpty
                    ? null
                    : _barcodeController.text.trim(),
                sku: _skuController.text.trim().isEmpty
                    ? null
                    : _skuController.text.trim(),
                description: _descriptionController.text.trim().isEmpty
                    ? null
                    : _descriptionController.text.trim(),
                baseUnitId: _selectedUnitId!,
                categoryId: _selectedCategoryId!,
                costPrice: double.parse(_costPriceController.text),
                sellingPrice: double.parse(_sellingPriceController.text),
                minStock: _trackStock && _minStockController.text.isNotEmpty
                    ? double.parse(_minStockController.text)
                    : 0.0,
                maxStock: _trackStock && _maxStockController.text.isNotEmpty
                    ? double.parse(_maxStockController.text)
                    : 0.0,
                isActive: _isActive,
                trackStock: _trackStock,
                // expiryDate: _expiryDate, // TODO: إضافة دعم تاريخ الانتهاء في createProduct
              );

          if (mounted) {
            if (result) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('تم حفظ المنتج "${_nameArController.text}" بنجاح'),
                  backgroundColor: AppColors.success,
                ),
              );
              Navigator.of(context).pop();
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('فشل في حفظ المنتج'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('حدث خطأ: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }
}
