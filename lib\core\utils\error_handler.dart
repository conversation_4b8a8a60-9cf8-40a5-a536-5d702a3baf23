import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../constants/app_constants.dart';
import 'app_utils.dart';

class ErrorHandler {
  // Handle general errors
  static String handleError(dynamic error) {
    AppUtils.logError('Error occurred', error);
    
    if (error is DioException) {
      return _handleDioError(error);
    } else if (error is FormatException) {
      return 'خطأ في تنسيق البيانات';
    } else if (error is TypeError) {
      return 'خطأ في نوع البيانات';
    } else if (error is ArgumentError) {
      return 'خطأ في المعاملات المرسلة';
    } else if (error is StateError) {
      return 'خطأ في حالة التطبيق';
    } else if (error is Exception) {
      return error.toString().replaceAll('Exception: ', '');
    } else {
      return AppConstants.serverErrorMessage;
    }
  }
  
  // Handle Dio (HTTP) errors
  static String _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'انتهت مهلة الاتصال';
      case DioExceptionType.sendTimeout:
        return 'انتهت مهلة إرسال البيانات';
      case DioExceptionType.receiveTimeout:
        return 'انتهت مهلة استقبال البيانات';
      case DioExceptionType.badResponse:
        return _handleHttpError(error.response?.statusCode);
      case DioExceptionType.cancel:
        return 'تم إلغاء العملية';
      case DioExceptionType.connectionError:
        return AppConstants.networkErrorMessage;
      case DioExceptionType.badCertificate:
        return 'خطأ في شهادة الأمان';
      case DioExceptionType.unknown:
      default:
        return AppConstants.serverErrorMessage;
    }
  }
  
  // Handle HTTP status codes
  static String _handleHttpError(int? statusCode) {
    switch (statusCode) {
      case 400:
        return 'طلب غير صحيح';
      case 401:
        return 'غير مخول للوصول';
      case 403:
        return AppConstants.permissionErrorMessage;
      case 404:
        return 'المورد غير موجود';
      case 405:
        return 'طريقة غير مسموحة';
      case 408:
        return 'انتهت مهلة الطلب';
      case 409:
        return 'تعارض في البيانات';
      case 422:
        return AppConstants.validationErrorMessage;
      case 429:
        return 'تم تجاوز حد الطلبات';
      case 500:
        return 'خطأ في الخادم الداخلي';
      case 502:
        return 'خطأ في البوابة';
      case 503:
        return 'الخدمة غير متاحة';
      case 504:
        return 'انتهت مهلة البوابة';
      default:
        return AppConstants.serverErrorMessage;
    }
  }
  
  // Show error dialog
  static void showErrorDialog(BuildContext context, String message, {String? title}) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? 'خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
  
  // Show error snackbar
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'إغلاق',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }
  
  // Handle validation errors
  static Map<String, String> handleValidationErrors(dynamic error) {
    Map<String, String> errors = {};
    
    if (error is DioException && error.response?.statusCode == 422) {
      final data = error.response?.data;
      if (data is Map<String, dynamic> && data.containsKey('errors')) {
        final validationErrors = data['errors'] as Map<String, dynamic>;
        validationErrors.forEach((key, value) {
          if (value is List && value.isNotEmpty) {
            errors[key] = value.first.toString();
          } else if (value is String) {
            errors[key] = value;
          }
        });
      }
    }
    
    return errors;
  }
  
  // Log error for debugging
  static void logError(String message, dynamic error, [StackTrace? stackTrace]) {
    AppUtils.logError(message, error, stackTrace);
    
    // In production, you might want to send this to a crash reporting service
    // like Firebase Crashlytics or Sentry
  }
  
  // Handle database errors
  static String handleDatabaseError(dynamic error) {
    AppUtils.logError('Database error', error);
    
    final errorMessage = error.toString().toLowerCase();
    
    if (errorMessage.contains('unique constraint')) {
      return 'البيانات موجودة مسبقاً';
    } else if (errorMessage.contains('foreign key constraint')) {
      return 'لا يمكن حذف هذا العنصر لأنه مرتبط ببيانات أخرى';
    } else if (errorMessage.contains('not null constraint')) {
      return 'يجب ملء جميع الحقول المطلوبة';
    } else if (errorMessage.contains('check constraint')) {
      return 'البيانات المدخلة غير صحيحة';
    } else if (errorMessage.contains('database is locked')) {
      return 'قاعدة البيانات مقفلة، يرجى المحاولة مرة أخرى';
    } else if (errorMessage.contains('no such table')) {
      return 'خطأ في هيكل قاعدة البيانات';
    } else if (errorMessage.contains('no such column')) {
      return 'خطأ في هيكل قاعدة البيانات';
    } else {
      return 'خطأ في قاعدة البيانات';
    }
  }
  
  // Handle file operation errors
  static String handleFileError(dynamic error) {
    AppUtils.logError('File error', error);
    
    final errorMessage = error.toString().toLowerCase();
    
    if (errorMessage.contains('permission denied')) {
      return 'ليس لديك صلاحية للوصول إلى هذا الملف';
    } else if (errorMessage.contains('file not found')) {
      return 'الملف غير موجود';
    } else if (errorMessage.contains('no space left')) {
      return 'لا توجد مساحة كافية على الجهاز';
    } else if (errorMessage.contains('file already exists')) {
      return 'الملف موجود مسبقاً';
    } else {
      return 'خطأ في العملية على الملف';
    }
  }
  
  // Handle sync errors
  static String handleSyncError(dynamic error) {
    AppUtils.logError('Sync error', error);
    
    if (error is DioException) {
      return _handleDioError(error);
    } else {
      return 'خطأ في المزامنة، يرجى المحاولة مرة أخرى';
    }
  }
  
  // Handle authentication errors
  static String handleAuthError(dynamic error) {
    AppUtils.logError('Auth error', error);
    
    if (error is DioException) {
      switch (error.response?.statusCode) {
        case 401:
          return 'اسم المستخدم أو كلمة المرور غير صحيحة';
        case 403:
          return 'ليس لديك صلاحية للوصول';
        case 429:
          return 'تم تجاوز حد محاولات تسجيل الدخول';
        default:
          return _handleDioError(error);
      }
    } else {
      return 'خطأ في تسجيل الدخول';
    }
  }
  
  // Handle permission errors
  static String handlePermissionError(dynamic error) {
    AppUtils.logError('Permission error', error);
    return AppConstants.permissionErrorMessage;
  }
  
  // Create custom exception
  static Exception createException(String message) {
    return Exception(message);
  }
  
  // Check if error is network related
  static bool isNetworkError(dynamic error) {
    if (error is DioException) {
      return error.type == DioExceptionType.connectionError ||
             error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.receiveTimeout ||
             error.type == DioExceptionType.sendTimeout;
    }
    return false;
  }
  
  // Check if error is server related
  static bool isServerError(dynamic error) {
    if (error is DioException && error.response != null) {
      final statusCode = error.response!.statusCode;
      return statusCode != null && statusCode >= 500;
    }
    return false;
  }
  
  // Check if error is client related
  static bool isClientError(dynamic error) {
    if (error is DioException && error.response != null) {
      final statusCode = error.response!.statusCode;
      return statusCode != null && statusCode >= 400 && statusCode < 500;
    }
    return false;
  }
}


class LoadingWidget extends StatelessWidget {
  const LoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}


class ErrorDisplayWidget extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error, color: Colors.red, size: 40),
          const SizedBox(height: 8),
          Text(
            'حدث خطأ: $error',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          ElevatedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}

