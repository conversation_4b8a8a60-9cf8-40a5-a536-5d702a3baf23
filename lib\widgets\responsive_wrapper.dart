import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsetsGeometry? padding;
  final bool centerContent;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.centerContent = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth >= 768;
    final isDesktop = screenWidth >= 1024;

    double effectiveMaxWidth = maxWidth ?? _getDefaultMaxWidth(screenWidth);

    Widget content = Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: effectiveMaxWidth,
      ),
      padding: padding ?? _getDefaultPadding(screenWidth),
      child: child,
    );

    if (centerContent && (isTablet || isDesktop)) {
      content = Center(child: content);
    }

    return content;
  }

  double _getDefaultMaxWidth(double screenWidth) {
    if (screenWidth >= 1200) {
      return 1200.w; // Desktop
    } else if (screenWidth >= 768) {
      return 768.w; // Tablet
    } else {
      return double.infinity; // Mobile
    }
  }

  EdgeInsetsGeometry _getDefaultPadding(double screenWidth) {
    if (screenWidth >= 1024) {
      return EdgeInsets.symmetric(horizontal: 32.w); // Desktop
    } else if (screenWidth >= 768) {
      return EdgeInsets.symmetric(horizontal: 24.w); // Tablet
    } else {
      return EdgeInsets.symmetric(horizontal: 16.w); // Mobile
    }
  }
}

class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, BoxConstraints constraints)
      builder;
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 1200 && desktop != null) {
          return desktop!;
        } else if (constraints.maxWidth >= 768 && tablet != null) {
          return tablet!;
        } else if (mobile != null) {
          return mobile!;
        } else {
          return builder(context, constraints);
        }
      },
    );
  }
}

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double runSpacing;
  final double childAspectRatio;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 1,
    this.tabletColumns = 2,
    this.desktopColumns = 3,
    this.spacing = 15.0,
    this.runSpacing = 15.0,
    this.childAspectRatio = 0.7,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        int columns;
        if (constraints.maxWidth >= 1024) {
          columns = desktopColumns;
        } else if (constraints.maxWidth >= 768) {
          columns = tabletColumns;
        } else {
          columns = mobileColumns;
        }

        // تعديل aspect ratio حسب حجم الشاشة
        double effectiveAspectRatio = childAspectRatio;
        if (constraints.maxWidth < 768) {
          // Mobile: نسبة أطول قليلاً
          effectiveAspectRatio = childAspectRatio * 1.1;
        } else if (constraints.maxWidth < 1024) {
          // Tablet: نسبة متوسطة
          effectiveAspectRatio = childAspectRatio * 1.05;
        }

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: columns,
          crossAxisSpacing: spacing.w,
          mainAxisSpacing: runSpacing.h,
          childAspectRatio: effectiveAspectRatio,
          children: children,
        );
      },
    );
  }
}

class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final bool wrapOnMobile;

  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.wrapOnMobile = true,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 768 && wrapOnMobile) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: children
                .map((child) => Padding(
                      padding: EdgeInsets.only(bottom: 8.h),
                      child: child,
                    ))
                .toList(),
          );
        } else {
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: children,
          );
        }
      },
    );
  }
}

class ResponsiveColumn extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final bool rowOnTablet;

  const ResponsiveColumn({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.rowOnTablet = false,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= 768 && rowOnTablet) {
          return Row(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: children.map((child) => Expanded(child: child)).toList(),
          );
        } else {
          return Column(
            mainAxisAlignment: mainAxisAlignment,
            crossAxisAlignment: crossAxisAlignment,
            children: children,
          );
        }
      },
    );
  }
}

// Responsive breakpoints
class ResponsiveBreakpoints {
  static const double mobile = 0;
  static const double tablet = 768;
  static const double desktop = 1024;
  static const double largeDesktop = 1440;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < tablet;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= tablet && width < desktop;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }

  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= largeDesktop;
  }
}

// Responsive values
class ResponsiveValue<T> {
  final T mobile;
  final T? tablet;
  final T? desktop;
  final T? largeDesktop;

  const ResponsiveValue({
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });

  T getValue(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    if (width >= ResponsiveBreakpoints.largeDesktop && largeDesktop != null) {
      return largeDesktop!;
    } else if (width >= ResponsiveBreakpoints.desktop && desktop != null) {
      return desktop!;
    } else if (width >= ResponsiveBreakpoints.tablet && tablet != null) {
      return tablet!;
    } else {
      return mobile;
    }
  }
}

// Extension for easy responsive values
extension ResponsiveExtension on BuildContext {
  T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    return ResponsiveValue<T>(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      largeDesktop: largeDesktop,
    ).getValue(this);
  }
}
