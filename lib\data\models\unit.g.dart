// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'unit.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Unit _$UnitFromJson(Map<String, dynamic> json) => Unit(
      id: json['id'] as String,
      name: json['name'] as String,
      symbol: json['symbol'] as String?,
      abbreviation: json['abbreviation'] as String?,
      factor: (json['factor'] as num).toDouble(),
      baseUnitId: json['baseUnitId'] as String?,
      unitType: json['unitType'] as String? ?? 'quantity',
      isBaseUnit: json['isBaseUnit'] as bool? ?? false,
      isActive: json['isActive'] as bool? ?? true,
      description: json['description'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      deletedAt: json['deletedAt'] == null
          ? null
          : DateTime.parse(json['deletedAt'] as String),
      isSynced: json['isSynced'] as bool? ?? false,
      baseUnit: json['baseUnit'] == null
          ? null
          : Unit.fromJson(json['baseUnit'] as Map<String, dynamic>),
      derivedUnits: (json['derivedUnits'] as List<dynamic>?)
          ?.map((e) => Unit.fromJson(e as Map<String, dynamic>))
          .toList(),
      usageCount: (json['usageCount'] as num?)?.toInt(),
    );

Map<String, dynamic> _$UnitToJson(Unit instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'symbol': instance.symbol,
      'abbreviation': instance.abbreviation,
      'factor': instance.factor,
      'baseUnitId': instance.baseUnitId,
      'unitType': instance.unitType,
      'isBaseUnit': instance.isBaseUnit,
      'isActive': instance.isActive,
      'description': instance.description,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'deletedAt': instance.deletedAt?.toIso8601String(),
      'isSynced': instance.isSynced,
      'baseUnit': instance.baseUnit,
      'derivedUnits': instance.derivedUnits,
      'usageCount': instance.usageCount,
    };
