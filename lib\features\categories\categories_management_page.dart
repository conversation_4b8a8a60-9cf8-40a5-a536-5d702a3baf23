import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:tijari_tech/data/models/category.dart';
import 'package:tijari_tech/features/categories/add_edite_category_dialog.dart';
import 'package:tijari_tech/router/routes.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/category_provider.dart';
import '../../widgets/main_layout.dart';

/// الصفحة الرئيسية لإدارة الفئات.
/// تعرض شجرة الفئات على اليسار،
/// وعند النقر على فئة تظهر صفحة منبثقة تنزلق من اليمين إلى اليسار
/// لعرض تفاصيل الفئة مع فروعها.
class CategoriesManagementPage extends ConsumerStatefulWidget {
  const CategoriesManagementPage({super.key});

  @override
  ConsumerState<CategoriesManagementPage> createState() =>
      _CategoriesManagementPageState();
}

class _CategoriesManagementPageState
    extends ConsumerState<CategoriesManagementPage> {
  final _searchController = TextEditingController();
  bool _showInactive = false;
  final Set<String> _expandedNodes = {}; // 1️⃣

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final categoryState = ref.watch(enhancedCategoryManagementProvider);

    return MainLayout(
      title: 'إدارة الفئات',
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddCategoryDialog(context),
        child: const Icon(Icons.add),
      ),

      /// الهيكل الأساسي: عمود يحتوي فقط على شجرة الفئات
      /// (تم حذف لوحة التفاصيل لأننا نستخدم صفحة منبثقة)
      child: _buildCategoriesTreePanel(context, categoryState),
    );
  }

  /* ---------------------------------------------------------- */
  /* ------------ 1. تصميم لوحة شجرة الفئات -------------------- */
  /* ---------------------------------------------------------- */
  Widget _buildCategoriesTreePanel(
      BuildContext context, EnhancedCategoryManagementState state) {
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTreePanelHeader(context, state), // شريط البحث
          SizedBox(height: 13.h),
          Expanded(child: _buildCategoriesTree(context, state)), // الشجرة
        ],
      ),
    );
  }

  /* ---------------------------------------------------------- */
  /* ------------ 2. شريط البحث والأدوات ---------------------- */
  /* ---------------------------------------------------------- */
  Widget _buildTreePanelHeader(
      BuildContext context, EnhancedCategoryManagementState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text('شجرة الفئات',
                style: AppTextStyles.titleLarge
                    .copyWith(fontWeight: FontWeight.bold)),
            const Spacer(),
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () => ref
                  .read(enhancedCategoryManagementProvider.notifier)
                  .refresh(),
              tooltip: 'تحديث',
            ),
            IconButton(
              icon: const Icon(Icons.expand_more),
              onPressed: _expandAll, // 3️⃣
              tooltip: 'توسيع الكل',
            ),
            IconButton(
              icon: const Icon(Icons.expand_less),
              onPressed: _collapseAll, // 3️⃣
              tooltip: 'طي الكل',
            ),
          ],
        ),
        SizedBox(height: 16.h),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'البحث في الفئات...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      ref
                          .read(enhancedCategoryManagementProvider.notifier)
                          .searchCategories('');
                    },
                  )
                : null,
            border:
                OutlineInputBorder(borderRadius: BorderRadius.circular(12.r)),
          ),
          onChanged: (value) => ref
              .read(enhancedCategoryManagementProvider.notifier)
              .searchCategories(value),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            FilterChip(
              label: const Text('إظهار غير النشطة'),
              selected: _showInactive,
              onSelected: (selected) {
                setState(() => _showInactive = selected);
                ref
                    .read(enhancedCategoryManagementProvider.notifier)
                    .toggleShowInactive(selected);
              },
            ),
            SizedBox(width: 8.w),
            Chip(
              label: Text('${state.filteredCategories.length} فئة'),
              backgroundColor: AppColors.primary.withValues(alpha: 0.1),
            ),
          ],
        ),
      ],
    );
  }

  /* ---------------------------------------------------------- */
  /* ------------ 3. بناء شجرة الفئات ------------------------- */
  /* ---------------------------------------------------------- */
  Widget _buildCategoriesTree(
      BuildContext context, EnhancedCategoryManagementState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (state.error != null) {
      return _errorWidget(state.error!);
    }
    if (state.categoriesTree.isEmpty) {
      return _emptyWidget(state.searchQuery);
    }

    return ListView.builder(
      itemCount: state.categoriesTree.length,
      itemBuilder: (_, index) =>
          _buildCategoryTreeItem(context, state.categoriesTree[index], 0),
    );
  }

  /* ---------------------------------------------------------- */
  /* ------------ 4. عنصر الفئة (مع الأبناء) ------------------ */
  /* ---------------------------------------------------------- */
  Widget _buildCategoryTreeItem(
      BuildContext context, Category category, int level) {
    final isExpanded = _isExpanded(category);
    final hasChildren = category.hasChildren;

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(left: (level * 20.0).w),
          child: Card(
            elevation: 1,
            child: ListTile(
              leading: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (hasChildren)
                    IconButton(
                      icon: Icon(
                        isExpanded ? Icons.expand_less : Icons.expand_more,
                        size: 20.r,
                        color: Colors.grey[600],
                      ),
                      onPressed: () => _toggleNode(category.id), // 2️⃣
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    )
                  else
                    SizedBox(width: 20.r),
                  Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: BoxDecoration(
                      color: category.color != null
                          ? Color(int.parse(
                              category.color!.replaceFirst('#', '0xFF')))
                          : AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
              title: Text(category.getDisplayName(),
                  style: AppTextStyles.bodyMedium),
              subtitle: category.productCount != null
                  ? Text('${category.productCount} منتج')
                  : null,
              trailing: _popupMenu(context, category),
              onTap: () => _openDetailsPage(context, category),
            ),
          ),
        ),
        // عرض الأبناء فقط إذا كانت مفتوحة
        if (isExpanded && hasChildren)
          ...category.children!.map(
              (child) => _buildCategoryTreeItem(context, child, level + 1)),
      ],
    );
  }

  /* ---------------------------------------------------------- */
  /* ------------ 5. منيو الإجراءات --------------------------- */
  /* ---------------------------------------------------------- */
  Widget _popupMenu(BuildContext context, Category category) {
    return PopupMenuButton<String>(
      onSelected: (value) => _handleCategoryAction(context, category, value),
      itemBuilder: (_) => [
        const PopupMenuItem(value: 'edit', child: Text('تعديل')),
        const PopupMenuItem(value: 'add_sub', child: Text('إضافة فرعية')),
        const PopupMenuItem(value: 'move', child: Text('نقل')),
        const PopupMenuItem(
            value: 'delete',
            child: Text('حذف', style: TextStyle(color: Colors.red))),
      ],
    );
  }

  /* ---------------------------------------------------------- */
  /* ------------ 6. فتح صفحة التفاصيل بالانزلاق ------------ */
  /* ---------------------------------------------------------- */
  void _openDetailsPage(BuildContext context, Category category) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (_, animation, __) =>
            _CategoryDetailsPage(category: category),
        transitionsBuilder: (_, animation, __, child) {
          const begin = Offset(1.0, 0.0); // من اليمين
          const end = Offset.zero;
          final tween = Tween(begin: begin, end: end)
              .chain(CurveTween(curve: Curves.easeInOut));
          final offsetAnimation = animation.drive(tween);
          return SlideTransition(position: offsetAnimation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  /* ---------------------------------------------------------- */
  /* ------------ 7. ودجتات مساعدة ---------------------------- */
  /* ---------------------------------------------------------- */
  Widget _errorWidget(String error) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64.r, color: AppColors.error),
            SizedBox(height: 16.h),
            Text('حدث خطأ', style: AppTextStyles.titleMedium),
            Text(error,
                style:
                    AppTextStyles.bodyMedium.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref
                  .read(enhancedCategoryManagementProvider.notifier)
                  .refresh(),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );

  Widget _emptyWidget(String searchQuery) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.category_outlined, size: 64.r, color: Colors.grey[400]),
            SizedBox(height: 16.h),
            Text(searchQuery.isNotEmpty ? 'لا توجد نتائج' : 'لا توجد فئات',
                style: AppTextStyles.titleMedium),
            Text(
                searchQuery.isNotEmpty
                    ? 'جرب البحث بكلمات أخرى'
                    : 'ابدأ بإضافة فئة جديدة',
                style:
                    AppTextStyles.bodyMedium.copyWith(color: Colors.grey[500])),
            if (searchQuery.isEmpty) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => _showAddCategoryDialog(context),
                child: const Text('إضافة فئة'),
              ),
            ],
          ],
        ),
      );

  /* ---------------------------------------------------------- */
  /* ------------ 8. إجراءات قوائم منبثقة -------------------- */
  /* ---------------------------------------------------------- */
  void _handleCategoryAction(
      BuildContext context, Category category, String action) {
    switch (action) {
      case 'edit':
        _showEditCategoryDialog(context, category);
        break;
      case 'add_sub':
        _showAddCategoryDialog(context, parentCategory: category);
        break;
      case 'move':
        context.showInfoSnackBar('نقل الفئة قيد التطوير');
        break;
      case 'delete':
        _showDeleteConfirmation(context, category);
        break;
    }
  }

  void _showAddCategoryDialog(BuildContext context,
      {Category? parentCategory}) {
    context.go(AppRoutes.categoryAdd, extra: parentCategory);
  }

  /// عرض حوار تعديل الفئة
  void _showEditCategoryDialog(BuildContext context, Category category) {
    context.go('/categories/edit/${category.id}');
  }

  void _showDeleteConfirmation(BuildContext context, Category category) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content:
            Text('هل أنت متأكد من حذف فئة "${category.getDisplayName()}"؟'),
        actions: [
          TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء')),
          ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: AppColors.error),
            onPressed: () {
              Navigator.of(context).pop();
              ref
                  .read(enhancedCategoryManagementProvider.notifier)
                  .deleteCategory(category.id);
              context.showSuccessSnackBar('تم حذف الفئة بنجاح');
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /* ----------  توسيع الكل / طي الكل ---------- */
  void _expandAll() {
    final notifier =
        ref.read(enhancedCategoryManagementProvider.notifier).expandAll();
    final allIds = notifier; // دالة تساعدية في الـ notifier
    setState(() => _expandedNodes.addAll(allIds));
  }

  void _collapseAll() {
    setState(() => _expandedNodes.clear());
  }

  /* ---------- تبديل حالة فردية ---------- */
  void _toggleNode(String id) {
    setState(() {
      _expandedNodes.contains(id)
          ? _expandedNodes.remove(id)
          : _expandedNodes.add(id);
    });
  }

  /* ---------- هل الفئة مفتوحة؟ ---------- */
  bool _isExpanded(Category c) => _expandedNodes.contains(c.id);
}

/* ------------------------------------------------------------------ */
/* ------------ صفحة التفاصيل المنبثقة مع السحب للإغلاق ------------ */
/* ------------------------------------------------------------------ */
class _CategoryDetailsPage extends StatelessWidget {
  final Category category;
  const _CategoryDetailsPage({required this.category});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(category.getDisplayName()),
        leading: IconButton(
          icon: const Icon(Icons.arrow_forward),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: GestureDetector(
        // السحب من اليمين لليسار لإغلاق الصفحة
        onHorizontalDragUpdate: (details) {
          if (details.primaryDelta! > 10) Navigator.of(context).pop();
        },
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.r),
          child: _CategoryDetailView(category: category),
        ),
      ),
    );
  }
}

class _CategoryDetailView extends StatelessWidget {
  final Category category;
  const _CategoryDetailView({required this.category});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _DetailCard(
          title: 'المعلومات الأساسية',
          children: [
            _DetailRow('الاسم بالعربية', category.nameAr),
            if (category.nameEn != null)
              _DetailRow('الاسم بالإنجليزية', category.nameEn!),
            if (category.description != null)
              _DetailRow('الوصف', category.description!),
            _DetailRow('الحالة', category.isActive ? 'نشط' : 'غير نشط'),
            _DetailRow('ترتيب العرض', category.sortOrder.toString()),
          ],
        ),
        SizedBox(height: 16.h),
        _DetailCard(
          title: 'الإحصائيات',
          children: [
            _DetailRow('عدد المنتجات', '${category.productCount ?? 0}'),
            _DetailRow(
                'عدد الفئات الفرعية', '${category.children?.length ?? 0}'),
            if (category.level != null)
              _DetailRow('المستوى', category.level.toString()),
          ],
        ),
        if (category.taxRate != null || category.discountRate != null) ...[
          SizedBox(height: 16.h),
          _DetailCard(
            title: 'الإعدادات المالية',
            children: [
              if (category.taxRate != null)
                _DetailRow('معدل الضريبة', '${category.taxRate}%'),
              if (category.discountRate != null)
                _DetailRow('معدل الخصم', '${category.discountRate}%'),
            ],
          ),
        ],
      ],
    );
  }
}

class _DetailCard extends StatelessWidget {
  final String title;
  final List<Widget> children;
  const _DetailCard({required this.title, required this.children});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title,
                style: AppTextStyles.titleMedium
                    .copyWith(fontWeight: FontWeight.bold)),
            SizedBox(height: 12.h),
            ...children,
          ],
        ),
      ),
    );
  }
}

class _DetailRow extends StatelessWidget {
  final String label, value;
  const _DetailRow(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
              width: 100.w,
              child: Text(label,
                  style: AppTextStyles.bodyMedium
                      .copyWith(color: Colors.grey[600]))),
          Expanded(child: Text(value, style: AppTextStyles.bodyMedium)),
        ],
      ),
    );
  }
}
