import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';

class CustomersPage extends ConsumerStatefulWidget {
  const CustomersPage({super.key});

  @override
  ConsumerState<CustomersPage> createState() => _CustomersPageState();
}

class _CustomersPageState extends ConsumerState<CustomersPage> {
  final _searchController = TextEditingController();
  String _selectedFilter = 'الكل';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'العملاء',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.customerAdd),
        child: const Icon(Icons.person_add),
      ),
      child: Column(
        children: [
          // Search and filter section
          _buildSearchSection(),
          
          // Customers list
          Expanded(
            child: _buildCustomersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[50],
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن عميل...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              // TODO: Implement search
            },
          ),
          SizedBox(height: 12.h),
          
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('الكل'),
                _buildFilterChip('نشط'),
                _buildFilterChip('غير نشط'),
                _buildFilterChip('مديون'),
                _buildFilterChip('دائن'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label) {
    final isSelected = _selectedFilter == label;
    return Padding(
      padding: EdgeInsets.only(right: 8.w),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = label;
          });
        },
        backgroundColor: Colors.white,
        selectedColor: AppColors.primary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.primary,
        labelStyle: AppTextStyles.bodySmall.copyWith(
          color: isSelected ? AppColors.primary : Colors.grey[700],
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildCustomersList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: _getSampleCustomers().length,
      itemBuilder: (context, index) {
        final customer = _getSampleCustomers()[index];
        return _buildCustomerCard(customer);
      },
    );
  }

  Widget _buildCustomerCard(Map<String, dynamic> customer) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: ListTile(
        contentPadding: EdgeInsets.all(16.r),
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Text(
            customer['name'].toString().substring(0, 1),
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          customer['name'],
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(Icons.phone, size: 14.r, color: Colors.grey[600]),
                SizedBox(width: 4.w),
                Text(
                  customer['phone'],
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Icon(Icons.account_balance_wallet, size: 14.r, color: Colors.grey[600]),
                SizedBox(width: 4.w),
                Text(
                  'الرصيد: ${customer['balance']} ر.س',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: customer['balance'].startsWith('-') 
                        ? AppColors.error 
                        : AppColors.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'view':
                context.go('${AppRoutes.customerView}/${customer['id']}');
                break;
              case 'edit':
                context.go('${AppRoutes.customerEdit}/${customer['id']}');
                break;
              case 'delete':
                _showDeleteDialog(customer);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility_outlined),
                  SizedBox(width: 8),
                  Text('عرض'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit_outlined),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outlined, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => context.go('${AppRoutes.customerView}/${customer['id']}'),
      ),
    );
  }

  void _showDeleteDialog(Map<String, dynamic> customer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف العميل "${customer['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement delete
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف العميل "${customer['name']}"'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getSampleCustomers() {
    return [
      {
        'id': '1',
        'name': 'أحمد محمد علي',
        'phone': '0501234567',
        'balance': '+2,500',
        'status': 'نشط',
      },
      {
        'id': '2',
        'name': 'فاطمة أحمد',
        'phone': '0507654321',
        'balance': '-1,200',
        'status': 'نشط',
      },
      {
        'id': '3',
        'name': 'محمد عبدالله',
        'phone': '0551234567',
        'balance': '+850',
        'status': 'نشط',
      },
      {
        'id': '4',
        'name': 'نورا سالم',
        'phone': '0567891234',
        'balance': '0',
        'status': 'نشط',
      },
      {
        'id': '5',
        'name': 'خالد العتيبي',
        'phone': '0512345678',
        'balance': '-3,750',
        'status': 'غير نشط',
      },
    ];
  }
}
