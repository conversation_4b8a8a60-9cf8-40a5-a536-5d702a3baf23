import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';

class SuppliersPage extends ConsumerStatefulWidget {
  const SuppliersPage({super.key});

  @override
  ConsumerState<SuppliersPage> createState() => _SuppliersPageState();
}

class _SuppliersPageState extends ConsumerState<SuppliersPage> {
  final _searchController = TextEditingController();
  String _selectedFilter = 'الكل';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'الموردين',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.supplierAdd),
        child: const Icon(Icons.business),
      ),
      child: Column(
        children: [
          // Search and filter section
          _buildSearchSection(),
          
          // Suppliers list
          Expanded(
            child: _buildSuppliersList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[50],
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث عن مورد...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12.r),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: (value) {
              // TODO: Implement search
            },
          ),
          SizedBox(height: 12.h),
          
          // Filter chips
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('الكل'),
                _buildFilterChip('نشط'),
                _buildFilterChip('غير نشط'),
                _buildFilterChip('مستحق'),
                _buildFilterChip('مدفوع'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label) {
    final isSelected = _selectedFilter == label;
    return Padding(
      padding: EdgeInsets.only(right: 8.w),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            _selectedFilter = label;
          });
        },
        backgroundColor: Colors.white,
        selectedColor: AppColors.secondary.withValues(alpha: 0.2),
        checkmarkColor: AppColors.secondary,
        labelStyle: AppTextStyles.bodySmall.copyWith(
          color: isSelected ? AppColors.secondary : Colors.grey[700],
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildSuppliersList() {
    return ListView.builder(
      padding: EdgeInsets.all(16.r),
      itemCount: _getSampleSuppliers().length,
      itemBuilder: (context, index) {
        final supplier = _getSampleSuppliers()[index];
        return _buildSupplierCard(supplier);
      },
    );
  }

  Widget _buildSupplierCard(Map<String, dynamic> supplier) {
    return Card(
      margin: EdgeInsets.only(bottom: 12.h),
      child: ListTile(
        contentPadding: EdgeInsets.all(16.r),
        leading: CircleAvatar(
          backgroundColor: AppColors.secondary.withValues(alpha: 0.1),
          child: Icon(
            Icons.business,
            color: AppColors.secondary,
            size: 24.r,
          ),
        ),
        title: Text(
          supplier['name'],
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            Row(
              children: [
                Icon(Icons.phone, size: 14.r, color: Colors.grey[600]),
                SizedBox(width: 4.w),
                Text(
                  supplier['phone'],
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Icon(Icons.category, size: 14.r, color: Colors.grey[600]),
                SizedBox(width: 4.w),
                Text(
                  supplier['category'],
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Icon(Icons.account_balance_wallet, size: 14.r, color: Colors.grey[600]),
                SizedBox(width: 4.w),
                Text(
                  'المستحق: ${supplier['balance']} ر.س',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: supplier['balance'].startsWith('-') 
                        ? AppColors.success 
                        : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'view':
                context.go('${AppRoutes.supplierView}/${supplier['id']}');
                break;
              case 'edit':
                context.go('${AppRoutes.supplierEdit}/${supplier['id']}');
                break;
              case 'delete':
                _showDeleteDialog(supplier);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: Row(
                children: [
                  Icon(Icons.visibility_outlined),
                  SizedBox(width: 8),
                  Text('عرض'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit_outlined),
                  SizedBox(width: 8),
                  Text('تعديل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outlined, color: Colors.red),
                  SizedBox(width: 8),
                  Text('حذف', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
        onTap: () => context.go('${AppRoutes.supplierView}/${supplier['id']}'),
      ),
    );
  }

  void _showDeleteDialog(Map<String, dynamic> supplier) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المورد "${supplier['name']}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement delete
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تم حذف المورد "${supplier['name']}"'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getSampleSuppliers() {
    return [
      {
        'id': '1',
        'name': 'شركة التوريد المحدودة',
        'phone': '0112345678',
        'category': 'مواد غذائية',
        'balance': '+15,750',
        'status': 'نشط',
      },
      {
        'id': '2',
        'name': 'مؤسسة الجودة التجارية',
        'phone': '0123456789',
        'category': 'مستلزمات طبية',
        'balance': '-8,200',
        'status': 'نشط',
      },
      {
        'id': '3',
        'name': 'شركة الإمداد الصناعي',
        'phone': '0134567890',
        'category': 'أدوات كهربائية',
        'balance': '+22,500',
        'status': 'نشط',
      },
      {
        'id': '4',
        'name': 'مجموعة النور التجارية',
        'phone': '0145678901',
        'category': 'مواد تنظيف',
        'balance': '0',
        'status': 'نشط',
      },
      {
        'id': '5',
        'name': 'شركة الخليج للتوريد',
        'phone': '0156789012',
        'category': 'مواد بناء',
        'balance': '+45,300',
        'status': 'غير نشط',
      },
    ];
  }
}
