import '../data/repositories/stock_repository.dart';
import '../data/repositories/warehouse_repository.dart';
import '../data/models/sale.dart';
import '../data/models/sale_item.dart';
import '../data/models/purchase.dart';
import '../data/models/purchase_item.dart';
import '../core/utils/app_utils.dart';
import '../core/exceptions/app_exceptions.dart';

/// خدمة ربط المخزون بالمبيعات والمشتريات
class InventoryIntegrationService {
  final StockRepository _stockRepository = StockRepository();
  final WarehouseRepository _warehouseRepository = WarehouseRepository();

  // ------------------------------------------------------------------
  // Sales Integration
  // ------------------------------------------------------------------

  /// معالجة مبيعة وتحديث المخزون
  Future<bool> processSale({
    required Sale sale,
    required List<SaleItem> saleItems,
    required String userId,
    String? warehouseId,
  }) async {
    try {
      // Get warehouse (use default if not specified)
      final warehouse = warehouseId != null
          ? await _warehouseRepository.getWarehouseById(warehouseId)
          : await _warehouseRepository.getDefaultWarehouse();

      if (warehouse == null) {
        throw ValidationException(
            'No warehouse specified or default warehouse not found');
      }

      // Process each sale item
      for (final saleItem in saleItems) {
        await _processSaleItem(
          saleItem: saleItem,
          sale: sale,
          warehouseId: warehouse.id,
          userId: userId,
        );
      }

      AppUtils.logInfo('Sale processed successfully: ${sale.id}');
      return true;
    } catch (e) {
      AppUtils.logError('Error processing sale', e);
      rethrow;
    }
  }

  /// معالجة عنصر مبيعة واحد
  Future<void> _processSaleItem({
    required SaleItem saleItem,
    required Sale sale,
    required String warehouseId,
    required String userId,
  }) async {
    // Check stock availability
    final currentStock = await _stockRepository.getStockByProductAndWarehouse(
      saleItem.productId,
      warehouseId,
    );

    if (currentStock == null ||
        currentStock.actualAvailableQuantity < saleItem.qty) {
      throw ValidationException(
          'Insufficient stock for product ${saleItem.productId}. '
          'Available: ${currentStock?.actualAvailableQuantity ?? 0}, '
          'Required: ${saleItem.qty}');
    }

    // Update stock with outbound movement
    await _stockRepository.updateStockWithMovement(
      productId: saleItem.productId,
      warehouseId: warehouseId,
      movementType: 'out',
      quantity: saleItem.qty,
      unitCost: saleItem.unitPrice,
      referenceType: 'sale',
      referenceId: sale.id,
      referenceNumber: sale.invoiceNo,
      notes: 'Sale to customer: ${sale.customerId}',
      createdBy: userId,
      branchId: sale.branchId,
    );
  }

  /// إلغاء مبيعة وإرجاع المخزون
  Future<bool> cancelSale({
    required Sale sale,
    required List<SaleItem> saleItems,
    required String userId,
    String? reason,
  }) async {
    try {
      // Get warehouse
      final warehouse = sale.branchId != null
          ? await _warehouseRepository.getWarehouseById(sale.branchId!)
          : await _warehouseRepository.getDefaultWarehouse();

      if (warehouse == null) {
        throw ValidationException('Warehouse not found for sale cancellation');
      }

      // Process each sale item (return to stock)
      for (final saleItem in saleItems) {
        await _stockRepository.updateStockWithMovement(
          productId: saleItem.productId,
          warehouseId: warehouse.id,
          movementType: 'in',
          quantity: saleItem.qty,
          unitCost: saleItem.unitPrice,
          referenceType: 'sale_cancellation',
          referenceId: sale.id,
          referenceNumber: sale.invoiceNo,
          notes: 'Sale cancellation - return to stock',
          reason: reason ?? 'Sale cancelled',
          createdBy: userId,
          branchId: sale.branchId,
        );
      }

      AppUtils.logInfo('Sale cancelled and stock returned: ${sale.id}');
      return true;
    } catch (e) {
      AppUtils.logError('Error cancelling sale', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Purchase Integration
  // ------------------------------------------------------------------

  /// معالجة مشتريات وتحديث المخزون
  Future<bool> processPurchase({
    required Purchase purchase,
    required List<PurchaseItem> purchaseItems,
    required String userId,
    String? warehouseId,
  }) async {
    try {
      // Get warehouse (use default if not specified)
      final warehouse = warehouseId != null
          ? await _warehouseRepository.getWarehouseById(warehouseId)
          : await _warehouseRepository.getDefaultWarehouse();

      if (warehouse == null) {
        throw ValidationException(
            'No warehouse specified or default warehouse not found');
      }

      // Process each purchase item
      for (final purchaseItem in purchaseItems) {
        await _processPurchaseItem(
          purchaseItem: purchaseItem,
          purchase: purchase,
          warehouseId: warehouse.id,
          userId: userId,
        );
      }

      AppUtils.logInfo('Purchase processed successfully: ${purchase.id}');
      return true;
    } catch (e) {
      AppUtils.logError('Error processing purchase', e);
      rethrow;
    }
  }

  /// معالجة عنصر مشتريات واحد
  Future<void> _processPurchaseItem({
    required PurchaseItem purchaseItem,
    required Purchase purchase,
    required String warehouseId,
    required String userId,
  }) async {
    // Update stock with inbound movement
    await _stockRepository.updateStockWithMovement(
      productId: purchaseItem.productId,
      warehouseId: warehouseId,
      movementType: 'in',
      quantity: purchaseItem.qty,
      unitCost: purchaseItem.unitPrice,
      referenceType: 'purchase',
      referenceId: purchase.id,
      referenceNumber: purchase.invoiceNo,
      notes: 'Purchase from supplier: ${purchase.supplierId}',
      createdBy: userId,
      branchId: purchase.branchId,
    );
  }

  /// إلغاء مشتريات وتقليل المخزون
  Future<bool> cancelPurchase({
    required Purchase purchase,
    required List<PurchaseItem> purchaseItems,
    required String userId,
    String? reason,
  }) async {
    try {
      // Get warehouse
      final warehouse = purchase.id != null
          ? await _warehouseRepository.getWarehouseById(purchase.id)
          : await _warehouseRepository.getDefaultWarehouse();

      if (warehouse == null) {
        throw ValidationException(
            'Warehouse not found for purchase cancellation');
      }

      // Process each purchase item (remove from stock)
      for (final purchaseItem in purchaseItems) {
        // Check if we have enough stock to remove
        final currentStock =
            await _stockRepository.getStockByProductAndWarehouse(
          purchaseItem.productId,
          warehouse.id,
        );

        if (currentStock == null ||
            currentStock.quantity < purchaseItem.qty) {
          throw ValidationException(
              'Cannot cancel purchase - insufficient stock for product ${purchaseItem.productId}');
        }

        await _stockRepository.updateStockWithMovement(
          productId: purchaseItem.productId,
          warehouseId: warehouse.id,
          movementType: 'out',
          quantity: purchaseItem.qty,
          unitCost: purchaseItem.unitPrice,
          referenceType: 'purchase_cancellation',
          referenceId: purchase.id,
          referenceNumber: purchase.invoiceNo,
          notes: 'Purchase cancellation - remove from stock',
          reason: reason ?? 'Purchase cancelled',
          createdBy: userId,
          branchId: purchase.branchId,
        );
      }

      AppUtils.logInfo('Purchase cancelled and stock adjusted: ${purchase.id}');
      return true;
    } catch (e) {
      AppUtils.logError('Error cancelling purchase', e);
      rethrow;
    }
  }

  // ------------------------------------------------------------------
  // Stock Validation
  // ------------------------------------------------------------------

  /// التحقق من توفر المخزون للمبيعة
  Future<Map<String, dynamic>> validateStockForSale({
    required List<SaleItem> saleItems,
    String? warehouseId,
  }) async {
    try {
      // Get warehouse
      final warehouse = warehouseId != null
          ? await _warehouseRepository.getWarehouseById(warehouseId)
          : await _warehouseRepository.getDefaultWarehouse();

      if (warehouse == null) {
        return {
          'valid': false,
          'error': 'No warehouse specified or default warehouse not found',
        };
      }

      final validationResults = <Map<String, dynamic>>[];
      bool allValid = true;

      for (final saleItem in saleItems) {
        final currentStock =
            await _stockRepository.getStockByProductAndWarehouse(
          saleItem.productId,
          warehouse.id,
        );

        final available = currentStock?.actualAvailableQuantity ?? 0;
        final isValid = available >= saleItem.qty;

        if (!isValid) allValid = false;

        validationResults.add({
          'product_id': saleItem.productId,
          'required_quantity': saleItem.qty,
          'available_quantity': available,
          'valid': isValid,
          'shortage': isValid ? 0 : saleItem.qty - available,
        });
      }

      return {
        'valid': allValid,
        'warehouse_id': warehouse.id,
        'warehouse_name': warehouse.name,
        'items': validationResults,
      };
    } catch (e) {
      AppUtils.logError('Error validating stock for sale', e);
      return {
        'valid': false,
        'error': 'Error validating stock: ${e.toString()}',
      };
    }
  }

  /// حجز المخزون للمبيعة
  Future<bool> reserveStockForSale({
    required List<SaleItem> saleItems,
    required String saleId,
    String? warehouseId,
  }) async {
    try {
      // Get warehouse
      final warehouse = warehouseId != null
          ? await _warehouseRepository.getWarehouseById(warehouseId)
          : await _warehouseRepository.getDefaultWarehouse();

      if (warehouse == null) {
        throw ValidationException(
            'No warehouse specified or default warehouse not found');
      }

      // Reserve stock for each item
      for (final saleItem in saleItems) {
        final currentStock =
            await _stockRepository.getStockByProductAndWarehouse(
          saleItem.productId,
          warehouse.id,
        );

        if (currentStock == null ||
            currentStock.actualAvailableQuantity < saleItem.qty) {
          throw ValidationException(
              'Cannot reserve stock - insufficient quantity for product ${saleItem.productId}');
        }

        // Update reserved quantity
        final updatedStock = currentStock.copyWith(
          reservedQuantity: currentStock.reservedQuantity + saleItem.qty,
          availableQuantity: currentStock.quantity -
              (currentStock.reservedQuantity + saleItem.qty),
          updatedAt: DateTime.now(),
          isSynced: false,
        );

        await _stockRepository.updateStock(currentStock.id, updatedStock);
      }

      AppUtils.logInfo('Stock reserved for sale: $saleId');
      return true;
    } catch (e) {
      AppUtils.logError('Error reserving stock for sale', e);
      rethrow;
    }
  }

  /// إلغاء حجز المخزون
  Future<bool> releaseReservedStock({
    required List<SaleItem> saleItems,
    required String saleId,
    String? warehouseId,
  }) async {
    try {
      // Get warehouse
      final warehouse = warehouseId != null
          ? await _warehouseRepository.getWarehouseById(warehouseId)
          : await _warehouseRepository.getDefaultWarehouse();

      if (warehouse == null) {
        throw ValidationException(
            'No warehouse specified or default warehouse not found');
      }

      // Release reserved stock for each item
      for (final saleItem in saleItems) {
        final currentStock =
            await _stockRepository.getStockByProductAndWarehouse(
          saleItem.productId,
          warehouse.id,
        );

        if (currentStock != null &&
            currentStock.reservedQuantity >= saleItem.qty) {
          // Update reserved quantity
          final updatedStock = currentStock.copyWith(
            reservedQuantity: currentStock.reservedQuantity - saleItem.qty,
            availableQuantity: currentStock.quantity -
                (currentStock.reservedQuantity - saleItem.qty),
            updatedAt: DateTime.now(),
            isSynced: false,
          );

          await _stockRepository.updateStock(currentStock.id, updatedStock);
        }
      }

      AppUtils.logInfo('Reserved stock released for sale: $saleId');
      return true;
    } catch (e) {
      AppUtils.logError('Error releasing reserved stock', e);
      rethrow;
    }
  }
}
