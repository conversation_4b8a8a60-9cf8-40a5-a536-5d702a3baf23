import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class AccountsPage extends ConsumerWidget {
  const AccountsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'الحسابات',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quick Stats
            _buildQuickStats(context),
            SizedBox(height: 24.h),
            
            // Account Types
            _buildAccountTypes(context),
            SizedBox(height: 24.h),
            
            // Recent Activities
            _buildRecentActivities(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملخص الحسابات',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          children: [
            _buildStatCard(
              context,
              title: 'إجمالي العملاء',
              value: '156',
              icon: Icons.people_outline,
              color: AppColors.primary,
            ),
            _buildStatCard(
              context,
              title: 'إجمالي الموردين',
              value: '43',
              icon: Icons.business_outlined,
              color: AppColors.secondary,
            ),
            _buildStatCard(
              context,
              title: 'مديونية العملاء',
              value: '45,200 ر.س',
              icon: Icons.account_balance_outlined,
              color: AppColors.success,
            ),
            _buildStatCard(
              context,
              title: 'مستحقات الموردين',
              value: '28,750 ر.س',
              icon: Icons.payment_outlined,
              color: AppColors.warning,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountTypes(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أنواع الحسابات',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 1,
          tabletColumns: 2,
          desktopColumns: 2,
          children: [
            _buildAccountTypeCard(
              context,
              title: 'العملاء',
              subtitle: 'إدارة بيانات العملاء والمبيعات',
              icon: Icons.people_outline,
              color: AppColors.primary,
              count: '156 عميل',
              onTap: () => context.go(AppRoutes.customers),
            ),
            _buildAccountTypeCard(
              context,
              title: 'الموردين',
              subtitle: 'إدارة بيانات الموردين والمشتريات',
              icon: Icons.business_outlined,
              color: AppColors.secondary,
              count: '43 مورد',
              onTap: () => context.go(AppRoutes.suppliers),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccountTypeCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String count,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(20.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12.r),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Icon(
                      icon,
                      size: 32.r,
                      color: color,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.r,
                    color: Colors.grey[400],
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Text(
                title,
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 12.h),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  count,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivities(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاطات الأخيرة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 6,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final activities = [
                {
                  'type': 'customer',
                  'title': 'عميل جديد',
                  'subtitle': 'تم إضافة العميل: أحمد محمد',
                  'time': 'منذ ساعتين',
                  'icon': Icons.person_add_outlined,
                  'color': AppColors.success,
                },
                {
                  'type': 'payment',
                  'title': 'دفعة جديدة',
                  'subtitle': 'تم استلام 2,500 ر.س من العميل: سارة أحمد',
                  'time': 'منذ 3 ساعات',
                  'icon': Icons.payment_outlined,
                  'color': AppColors.primary,
                },
                {
                  'type': 'supplier',
                  'title': 'مورد جديد',
                  'subtitle': 'تم إضافة المورد: شركة التوريد المحدودة',
                  'time': 'منذ 5 ساعات',
                  'icon': Icons.business_outlined,
                  'color': AppColors.info,
                },
              ];
              
              final activity = activities[index % activities.length];
              
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: (activity['color'] as Color).withValues(alpha: 0.1),
                  child: Icon(
                    activity['icon'] as IconData,
                    color: activity['color'] as Color,
                    size: 20.r,
                  ),
                ),
                title: Text(
                  activity['title'] as String,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      activity['subtitle'] as String,
                      style: AppTextStyles.bodySmall,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      activity['time'] as String,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
                onTap: () {
                  // TODO: Navigate to activity details
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
