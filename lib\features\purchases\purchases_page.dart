import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../router/routes.dart';
import '../../widgets/main_layout.dart';
import '../../widgets/responsive_wrapper.dart';

class PurchasesPage extends ConsumerWidget {
  const PurchasesPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MainLayout(
      title: 'المشتريات',
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.go(AppRoutes.purchaseAdd),
        child: const Icon(Icons.add_shopping_cart),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quick Stats
            _buildQuickStats(context),
            SizedBox(height: 24.h),

            // Quick Actions
            _buildQuickActions(context),
            SizedBox(height: 24.h),

            // Recent Purchases
            _buildRecentPurchases(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إحصائيات المشتريات',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 4,
          desktopColumns: 4,
          childAspectRatio: 1.4,
          children: [
            _buildStatCard(
              context,
              title: 'مشتريات اليوم',
              value: '8,750 ر.س',
              icon: Icons.shopping_cart_outlined,
              color: AppColors.primary,
            ),
            _buildStatCard(
              context,
              title: 'عدد الفواتير',
              value: '23',
              icon: Icons.receipt_long_outlined,
              color: AppColors.success,
            ),
            _buildStatCard(
              context,
              title: 'متوسط الفاتورة',
              value: '380 ر.س',
              icon: Icons.analytics_outlined,
              color: AppColors.info,
            ),
            _buildStatCard(
              context,
              title: 'مبالغ مستحقة',
              value: '15,200 ر.س',
              icon: Icons.pending_outlined,
              color: AppColors.warning,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32.r,
              color: color,
            ),
            SizedBox(height: 8.h),
            Text(
              value,
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              title,
              style: AppTextStyles.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 16.h),
        ResponsiveGrid(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          children: [
            _buildActionCard(
              context,
              title: 'فاتورة شراء جديدة',
              subtitle: 'إنشاء فاتورة شراء جديدة',
              icon: Icons.add_shopping_cart,
              color: AppColors.primary,
              onTap: () => context.go(AppRoutes.purchaseAdd),
            ),
            _buildActionCard(
              context,
              title: 'قائمة المشتريات',
              subtitle: 'عرض جميع فواتير الشراء',
              icon: Icons.list_alt_outlined,
              color: AppColors.secondary,
              onTap: () => context.go(AppRoutes.purchaseList),
            ),
            _buildActionCard(
              context,
              title: 'المشتريات المعلقة',
              subtitle: 'الفواتير غير المدفوعة',
              icon: Icons.pending_actions_outlined,
              color: AppColors.warning,
              onTap: () {
                // TODO: Navigate to pending purchases
              },
            ),
            _buildActionCard(
              context,
              title: 'مرتجعات المشتريات',
              subtitle: 'إدارة مرتجعات الموردين',
              icon: Icons.keyboard_return_outlined,
              color: AppColors.error,
              onTap: () => context.go(AppRoutes.purchaseReturn),
            ),
            _buildActionCard(
              context,
              title: 'تقرير المشتريات',
              subtitle: 'تقارير تفصيلية للمشتريات',
              icon: Icons.analytics_outlined,
              color: AppColors.info,
              onTap: () => context.go(AppRoutes.purchaseReport),
            ),
            _buildActionCard(
              context,
              title: 'الموردين',
              subtitle: 'إدارة بيانات الموردين',
              icon: Icons.business_outlined,
              color: AppColors.accent,
              onTap: () => context.go(AppRoutes.suppliers),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32.r,
                color: color,
              ),
              SizedBox(height: 8.h),
              Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 4.h),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRecentPurchases(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'المشتريات الأخيرة',
              style: AppTextStyles.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () => context.go(AppRoutes.purchaseList),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 16.h),
        Card(
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final isPaid = index % 3 == 0;
              final isPartiallyPaid = index % 3 == 1;

              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: AppColors.primary.withValues(alpha: 0.1),
                  child: Icon(
                    Icons.shopping_cart_outlined,
                    color: AppColors.primary,
                    size: 20.r,
                  ),
                ),
                title: Text(
                  'فاتورة شراء #PUR00012${index + 1}',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                subtitle: Text(
                  'مورد: شركة التوريد المحدودة - ${(850.0 * (index + 1)).toStringAsFixed(0)} ر.س',
                  style: AppTextStyles.bodySmall,
                ),
                trailing: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: isPaid
                        ? AppColors.success
                        : isPartiallyPaid
                            ? AppColors.warning
                            : AppColors.error,
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    isPaid
                        ? 'مدفوع'
                        : isPartiallyPaid
                            ? 'جزئي'
                            : 'معلق',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                onTap: () {
                  // TODO: Navigate to purchase details
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
