import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import '../../providers/ai_provider.dart';
import '../../data/models/ai_prediction.dart';

class AIDashboardPage extends ConsumerStatefulWidget {
  const AIDashboardPage({super.key});

  @override
  ConsumerState<AIDashboardPage> createState() => _AIDashboardPageState();
}

class _AIDashboardPageState extends ConsumerState<AIDashboardPage> {
  String _selectedPredictionType = PredictionType.sales;
  String _selectedPeriod = PredictionPeriod.monthly;

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'لوحة التحكم الذكية',
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 24.h),
            _buildQuickActions(),
            SizedBox(height: 24.h),
            _buildPredictionsSection(),
            SizedBox(height: 24.h),
            _buildInsightsSection(),
            SizedBox(height: 24.h),
            _buildPerformanceMetrics(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(20.r),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primary,
            AppColors.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(12.r),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.psychology,
              size: 32.r,
              color: Colors.white,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الذكاء الاصطناعي للأعمال',
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'تحليلات ذكية وتوقعات دقيقة لنمو أعمالك',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Colors.white.withValues(alpha: 0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                title: 'توليد توقعات',
                subtitle: 'إنشاء توقعات جديدة',
                icon: Icons.auto_awesome,
                color: AppColors.success,
                onTap: () => _showGeneratePredictionsDialog(),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildActionCard(
                title: 'تحليل البيانات',
                subtitle: 'تحليل شامل للأداء',
                icon: Icons.analytics,
                color: AppColors.info,
                onTap: () => _generateInsights(),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildActionCard(
                title: 'تقرير ذكي',
                subtitle: 'تقرير مفصل بالتوصيات',
                icon: Icons.assessment,
                color: AppColors.warning,
                onTap: () => _generateSmartReport(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.all(16.r),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(8.r),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 24.r,
                color: color,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              title,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 2.h),
            Text(
              subtitle,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التوقعات الذكية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: () => _showAllPredictions(),
              icon: const Icon(Icons.arrow_forward),
              label: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Consumer(
          builder: (context, ref, child) {
            final dashboardDataAsync = ref.watch(aiDashboardDataProvider);
            
            return dashboardDataAsync.when(
              data: (data) {
                final predictions = data['latest_predictions'] as Map<String, dynamic>;
                return _buildPredictionsGrid(predictions);
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorWidget('فشل في تحميل التوقعات'),
            );
          },
        ),
      ],
    );
  }

  Widget _buildPredictionsGrid(Map<String, dynamic> predictions) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 12.h,
      crossAxisSpacing: 12.w,
      childAspectRatio: 1.2,
      children: [
        _buildPredictionCard(
          title: 'توقعات المبيعات',
          prediction: predictions['sales'] as AIPrediction?,
          icon: Icons.trending_up,
          color: AppColors.success,
        ),
        _buildPredictionCard(
          title: 'توقعات المخزون',
          prediction: predictions['inventory'] as AIPrediction?,
          icon: Icons.inventory,
          color: AppColors.warning,
        ),
        _buildPredictionCard(
          title: 'التدفق النقدي',
          prediction: predictions['cash_flow'] as AIPrediction?,
          icon: Icons.account_balance_wallet,
          color: AppColors.info,
        ),
        _buildPredictionCard(
          title: 'سلوك العملاء',
          prediction: null, // Will be generated
          icon: Icons.people,
          color: AppColors.secondary,
        ),
      ],
    );
  }

  Widget _buildPredictionCard({
    required String title,
    required AIPrediction? prediction,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.r),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(
                  icon,
                  size: 20.r,
                  color: color,
                ),
              ),
              const Spacer(),
              if (prediction != null)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: _getConfidenceColor(prediction.confidence).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    '${(prediction.confidence * 100).toInt()}%',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: _getConfidenceColor(prediction.confidence),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 4.h),
          if (prediction != null) ...[
            Text(
              prediction.confidenceLevel,
              style: AppTextStyles.bodySmall.copyWith(
                color: _getConfidenceColor(prediction.confidence),
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'آخر تحديث: ${_formatDate(prediction.createdAt)}',
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ] else ...[
            Text(
              'لم يتم إنشاء توقع بعد',
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            TextButton(
              onPressed: () => _generateSpecificPrediction(title),
              child: const Text('إنشاء توقع'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInsightsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الرؤى الذكية',
              style: AppTextStyles.titleMedium.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton.icon(
              onPressed: () => _showAllInsights(),
              icon: const Icon(Icons.arrow_forward),
              label: const Text('عرض الكل'),
            ),
          ],
        ),
        SizedBox(height: 12.h),
        Consumer(
          builder: (context, ref, child) {
            final insightsAsync = ref.watch(aiInsightsProvider);
            
            return insightsAsync.when(
              data: (insights) {
                if (insights.isEmpty) {
                  return _buildEmptyInsights();
                }
                return _buildInsightsList(insights.take(3).toList());
              },
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorWidget('فشل في تحميل الرؤى'),
            );
          },
        ),
      ],
    );
  }

  Widget _buildInsightsList(List<AIInsight> insights) {
    return Column(
      children: insights.map((insight) => _buildInsightCard(insight)).toList(),
    );
  }

  Widget _buildInsightCard(AIInsight insight) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: _getPriorityColor(insight.priority).withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: _getPriorityColor(insight.priority).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  insight.displayPriority,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _getPriorityColor(insight.priority),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: _getCategoryColor(insight.category).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  insight.displayCategory,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _getCategoryColor(insight.category),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            insight.title,
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            insight.description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[700],
            ),
          ),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.all(12.r),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  size: 16.r,
                  color: AppColors.info,
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    insight.recommendation,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.info,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInsights() {
    return Container(
      padding: EdgeInsets.all(32.r),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 48.r,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد رؤى متاحة',
            style: AppTextStyles.titleMedium.copyWith(
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'انقر على "تحليل البيانات" لإنشاء رؤى ذكية',
            style: AppTextStyles.bodyMedium.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton.icon(
            onPressed: () => _generateInsights(),
            icon: const Icon(Icons.analytics),
            label: const Text('تحليل البيانات'),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مقاييس الأداء',
          style: AppTextStyles.titleMedium.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Consumer(
          builder: (context, ref, child) {
            final metricsAsync = ref.watch(aiPerformanceMetricsProvider);
            
            return metricsAsync.when(
              data: (metrics) => _buildMetricsGrid(metrics),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => _buildErrorWidget('فشل في تحميل المقاييس'),
            );
          },
        ),
      ],
    );
  }

  Widget _buildMetricsGrid(Map<String, dynamic> metrics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4,
      mainAxisSpacing: 12.h,
      crossAxisSpacing: 12.w,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          title: 'إجمالي التوقعات',
          value: '${metrics['total_predictions']}',
          icon: Icons.psychology,
          color: AppColors.primary,
        ),
        _buildMetricCard(
          title: 'عالية الثقة',
          value: '${metrics['high_confidence_count']}',
          icon: Icons.verified,
          color: AppColors.success,
        ),
        _buildMetricCard(
          title: 'متوسطة الثقة',
          value: '${metrics['medium_confidence_count']}',
          icon: Icons.help_outline,
          color: AppColors.warning,
        ),
        _buildMetricCard(
          title: 'منخفضة الثقة',
          value: '${metrics['low_confidence_count']}',
          icon: Icons.error_outline,
          color: AppColors.error,
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 24.r,
            color: color,
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Container(
      padding: EdgeInsets.all(32.r),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 48.r,
            color: AppColors.error,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.error,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return AppColors.success;
    if (confidence >= 0.6) return AppColors.warning;
    return AppColors.error;
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case InsightPriority.high:
        return AppColors.error;
      case InsightPriority.medium:
        return AppColors.warning;
      case InsightPriority.low:
        return AppColors.info;
      default:
        return Colors.grey;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case InsightCategory.performance:
        return AppColors.success;
      case InsightCategory.opportunity:
        return AppColors.info;
      case InsightCategory.risk:
        return AppColors.error;
      case InsightCategory.trend:
        return AppColors.warning;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Action methods
  void _showGeneratePredictionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('توليد توقعات جديدة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedPredictionType,
              decoration: const InputDecoration(
                labelText: 'نوع التوقع',
                border: OutlineInputBorder(),
              ),
              items: PredictionType.allTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_getPredictionTypeDisplayName(type)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPredictionType = value!;
                });
              },
            ),
            SizedBox(height: 16.h),
            DropdownButtonFormField<String>(
              value: _selectedPeriod,
              decoration: const InputDecoration(
                labelText: 'الفترة الزمنية',
                border: OutlineInputBorder(),
              ),
              items: PredictionPeriod.allPeriods.map((period) {
                return DropdownMenuItem(
                  value: period,
                  child: Text(_getPeriodDisplayName(period)),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPeriod = value!;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _generatePrediction(_selectedPredictionType, _selectedPeriod);
            },
            child: const Text('توليد'),
          ),
        ],
      ),
    );
  }

  void _generatePrediction(String type, String period) async {
    final targetDate = DateTime.now().add(const Duration(days: 30));
    
    try {
      switch (type) {
        case PredictionType.sales:
          await ref.read(aiPredictionsProvider.notifier).generateSalesPrediction(
            period: period,
            targetDate: targetDate,
          );
          break;
        case PredictionType.inventory:
          await ref.read(aiPredictionsProvider.notifier).generateInventoryPrediction(
            period: period,
            targetDate: targetDate,
          );
          break;
        case PredictionType.cashFlow:
          await ref.read(aiPredictionsProvider.notifier).generateCashFlowPrediction(
            period: period,
            targetDate: targetDate,
          );
          break;
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم توليد التوقع بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
      
      // Refresh dashboard data
      ref.refresh(aiDashboardDataProvider);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في توليد التوقع: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _generateInsights() async {
    try {
      await ref.read(aiInsightsProvider.notifier).generateInsights();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('تم تحليل البيانات وإنشاء الرؤى بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تحليل البيانات: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _generateSmartReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة التقرير الذكي قيد التطوير')),
    );
  }

  void _generateSpecificPrediction(String title) {
    // Implementation for generating specific prediction
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('توليد توقع $title قيد التطوير')),
    );
  }

  void _showAllPredictions() {
    // Navigate to predictions page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('صفحة جميع التوقعات قيد التطوير')),
    );
  }

  void _showAllInsights() {
    // Navigate to insights page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('صفحة جميع الرؤى قيد التطوير')),
    );
  }

  String _getPredictionTypeDisplayName(String type) {
    switch (type) {
      case PredictionType.sales:
        return 'توقعات المبيعات';
      case PredictionType.inventory:
        return 'توقعات المخزون';
      case PredictionType.cashFlow:
        return 'التدفق النقدي';
      case PredictionType.customerBehavior:
        return 'سلوك العملاء';
      default:
        return type;
    }
  }

  String _getPeriodDisplayName(String period) {
    switch (period) {
      case PredictionPeriod.daily:
        return 'يومي';
      case PredictionPeriod.weekly:
        return 'أسبوعي';
      case PredictionPeriod.monthly:
        return 'شهري';
      case PredictionPeriod.quarterly:
        return 'ربع سنوي';
      case PredictionPeriod.yearly:
        return 'سنوي';
      default:
        return period;
    }
  }
}
