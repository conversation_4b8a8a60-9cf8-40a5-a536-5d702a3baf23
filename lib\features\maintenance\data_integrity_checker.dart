import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../core/utils/extensions.dart';
import '../../providers/enhanced_product_provider.dart';
import '../../providers/category_provider.dart';
import '../../providers/unit_provider.dart';
import '../../widgets/main_layout.dart';

class DataIntegrityChecker extends ConsumerStatefulWidget {
  const DataIntegrityChecker({super.key});

  @override
  ConsumerState<DataIntegrityChecker> createState() => _DataIntegrityCheckerState();
}

class _DataIntegrityCheckerState extends ConsumerState<DataIntegrityChecker> {
  bool _isChecking = false;
  bool _isFixing = false;
  List<DataIntegrityIssue> _issues = [];
  Map<String, bool> _selectedIssues = {};

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'فحص سلامة البيانات',
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with actions
            _buildHeader(),
            SizedBox(height: 16.h),
            
            // Status summary
            _buildStatusSummary(),
            SizedBox(height: 16.h),
            
            // Issues list
            Expanded(
              child: _buildIssuesList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.health_and_safety,
                  color: AppColors.primary,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'فحص سلامة البيانات',
                  style: AppTextStyles.titleLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const Spacer(),
                
                if (_issues.isNotEmpty && !_isChecking)
                  Text(
                    '${_issues.length} مشكلة',
                    style: AppTextStyles.titleMedium.copyWith(
                      color: _issues.any((issue) => issue.severity == IssueSeverity.critical)
                          ? Colors.red
                          : _issues.any((issue) => issue.severity == IssueSeverity.warning)
                              ? Colors.orange
                              : Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 12.h),
            
            Text(
              'يقوم هذا النظام بفحص قاعدة البيانات للتأكد من سلامة البيانات وتماسكها',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16.h),
            
            // Action buttons
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isChecking ? null : _runIntegrityCheck,
                  icon: _isChecking 
                      ? SizedBox(
                          width: 16.w,
                          height: 16.h,
                          child: const CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search),
                  label: const Text('فحص البيانات'),
                ),
                SizedBox(width: 16.w),
                
                if (_issues.isNotEmpty && _selectedIssues.values.any((selected) => selected))
                  ElevatedButton.icon(
                    onPressed: _isFixing ? null : _fixSelectedIssues,
                    icon: _isFixing 
                        ? SizedBox(
                            width: 16.w,
                            height: 16.h,
                            child: const CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.build),
                    label: Text('إصلاح المحدد (${_selectedIssues.values.where((v) => v).length})'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                  ),
                
                const Spacer(),
                
                OutlinedButton.icon(
                  onPressed: _exportReport,
                  icon: const Icon(Icons.download),
                  label: const Text('تصدير التقرير'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSummary() {
    if (_issues.isEmpty && !_isChecking) {
      return Card(
        color: Colors.green[50],
        child: Padding(
          padding: EdgeInsets.all(16.r),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 32.r,
              ),
              SizedBox(width: 12.w),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'البيانات سليمة',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: Colors.green[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'لم يتم العثور على أي مشاكل في سلامة البيانات',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: Colors.green[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_issues.isEmpty) {
      return const SizedBox.shrink();
    }

    final criticalCount = _issues.where((issue) => issue.severity == IssueSeverity.critical).length;
    final warningCount = _issues.where((issue) => issue.severity == IssueSeverity.warning).length;
    final infoCount = _issues.where((issue) => issue.severity == IssueSeverity.info).length;

    return Row(
      children: [
        if (criticalCount > 0)
          Expanded(
            child: _buildSummaryCard(
              'مشاكل حرجة',
              criticalCount.toString(),
              Colors.red,
              Icons.error,
            ),
          ),
        
        if (criticalCount > 0 && warningCount > 0) SizedBox(width: 8.w),
        
        if (warningCount > 0)
          Expanded(
            child: _buildSummaryCard(
              'تحذيرات',
              warningCount.toString(),
              Colors.orange,
              Icons.warning,
            ),
          ),
        
        if ((criticalCount > 0 || warningCount > 0) && infoCount > 0) SizedBox(width: 8.w),
        
        if (infoCount > 0)
          Expanded(
            child: _buildSummaryCard(
              'معلومات',
              infoCount.toString(),
              Colors.blue,
              Icons.info,
            ),
          ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String count, Color color, IconData icon) {
    return Card(
      color: color.withValues(alpha: 0.1),
      child: Padding(
        padding: EdgeInsets.all(16.r),
        child: Row(
          children: [
            Icon(
              icon,
              color: color,
              size: 24.r,
            ),
            SizedBox(width: 8.w),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    count,
                    style: AppTextStyles.titleLarge.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: color,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIssuesList() {
    if (_isChecking) {
      return Card(
        child: Padding(
          padding: EdgeInsets.all(32.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              SizedBox(height: 16.h),
              Text(
                'جاري فحص البيانات...',
                style: AppTextStyles.titleMedium,
              ),
              SizedBox(height: 8.h),
              Text(
                'قد تستغرق هذه العملية بضع دقائق',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_issues.isEmpty) {
      return Card(
        child: Padding(
          padding: EdgeInsets.all(32.r),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search,
                size: 64.r,
                color: Colors.grey[400],
              ),
              SizedBox(height: 16.h),
              Text(
                'لم يتم إجراء فحص بعد',
                style: AppTextStyles.titleMedium.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'اضغط على "فحص البيانات" لبدء الفحص',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Column(
        children: [
          // Header with select all
          Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: _selectedIssues.values.every((selected) => selected),
                  onChanged: (value) {
                    setState(() {
                      for (final issue in _issues) {
                        _selectedIssues[issue.id] = value ?? false;
                      }
                    });
                  },
                ),
                SizedBox(width: 8.w),
                Text(
                  'تحديد الكل',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  '${_issues.length} مشكلة',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          // Issues list
          Expanded(
            child: ListView.builder(
              itemCount: _issues.length,
              itemBuilder: (context, index) {
                final issue = _issues[index];
                return _buildIssueItem(issue);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIssueItem(DataIntegrityIssue issue) {
    final isSelected = _selectedIssues[issue.id] ?? false;
    
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: ListTile(
        leading: Checkbox(
          value: isSelected,
          onChanged: issue.canAutoFix ? (value) {
            setState(() {
              _selectedIssues[issue.id] = value ?? false;
            });
          } : null,
        ),
        title: Row(
          children: [
            _buildSeverityIcon(issue.severity),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                issue.title,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (issue.canAutoFix)
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  'قابل للإصلاح',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 4.h),
            Text(
              issue.description,
              style: AppTextStyles.bodyMedium,
            ),
            if (issue.affectedItems.isNotEmpty) ...[
              SizedBox(height: 4.h),
              Text(
                'العناصر المتأثرة: ${issue.affectedItems.join(", ")}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ],
            if (issue.recommendation.isNotEmpty) ...[
              SizedBox(height: 4.h),
              Text(
                'التوصية: ${issue.recommendation}',
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.blue[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
        trailing: issue.canAutoFix
            ? IconButton(
                icon: const Icon(Icons.build),
                onPressed: () => _fixSingleIssue(issue),
                tooltip: 'إصلاح',
              )
            : Icon(
                Icons.info_outline,
                color: Colors.grey[400],
              ),
      ),
    );
  }

  Widget _buildSeverityIcon(IssueSeverity severity) {
    switch (severity) {
      case IssueSeverity.critical:
        return Icon(
          Icons.error,
          color: Colors.red,
          size: 20.r,
        );
      case IssueSeverity.warning:
        return Icon(
          Icons.warning,
          color: Colors.orange,
          size: 20.r,
        );
      case IssueSeverity.info:
        return Icon(
          Icons.info,
          color: Colors.blue,
          size: 20.r,
        );
    }
  }

  Future<void> _runIntegrityCheck() async {
    setState(() {
      _isChecking = true;
      _issues.clear();
      _selectedIssues.clear();
    });

    try {
      // Check products integrity
      final productIssues = await ref.read(enhancedProductManagementProvider.notifier).validateDataIntegrity();
      
      // Check categories integrity
      final categoryIssues = await ref.read(enhancedCategoryManagementProvider.notifier).validateDataIntegrity();
      
      // Check units integrity
      final unitIssues = await ref.read(enhancedUnitManagementProvider.notifier).validateDataIntegrity();
      
      // Convert to DataIntegrityIssue objects
      final allIssues = <DataIntegrityIssue>[];
      
      // Add product issues
      for (final issue in productIssues) {
        allIssues.add(DataIntegrityIssue.fromMap(issue, 'products'));
      }
      
      // Add category issues
      for (final issue in categoryIssues) {
        allIssues.add(DataIntegrityIssue.fromMap(issue, 'categories'));
      }
      
      // Add unit issues
      for (final issue in unitIssues) {
        allIssues.add(DataIntegrityIssue.fromMap(issue, 'units'));
      }
      
      setState(() {
        _issues = allIssues;
        // Initialize selection state
        for (final issue in _issues) {
          _selectedIssues[issue.id] = false;
        }
      });
      
      if (_issues.isEmpty) {
        context.showSuccessSnackBar('تم الفحص بنجاح - لا توجد مشاكل');
      } else {
        context.showInfoSnackBar('تم العثور على ${_issues.length} مشكلة');
      }
    } catch (e) {
      context.showErrorSnackBar('فشل في فحص البيانات: ${e.toString()}');
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }

  Future<void> _fixSelectedIssues() async {
    final selectedIssues = _issues.where((issue) => _selectedIssues[issue.id] == true).toList();
    
    if (selectedIssues.isEmpty) {
      context.showWarningSnackBar('لم يتم تحديد أي مشاكل للإصلاح');
      return;
    }

    setState(() {
      _isFixing = true;
    });

    try {
      int fixedCount = 0;
      
      for (final issue in selectedIssues) {
        if (await _fixIssue(issue)) {
          fixedCount++;
          setState(() {
            _issues.remove(issue);
            _selectedIssues.remove(issue.id);
          });
        }
      }
      
      if (fixedCount > 0) {
        context.showSuccessSnackBar('تم إصلاح $fixedCount مشكلة');
      } else {
        context.showWarningSnackBar('لم يتم إصلاح أي مشكلة');
      }
    } catch (e) {
      context.showErrorSnackBar('فشل في إصلاح المشاكل: ${e.toString()}');
    } finally {
      setState(() {
        _isFixing = false;
      });
    }
  }

  Future<void> _fixSingleIssue(DataIntegrityIssue issue) async {
    try {
      if (await _fixIssue(issue)) {
        setState(() {
          _issues.remove(issue);
          _selectedIssues.remove(issue.id);
        });
        context.showSuccessSnackBar('تم إصلاح المشكلة');
      } else {
        context.showErrorSnackBar('فشل في إصلاح المشكلة');
      }
    } catch (e) {
      context.showErrorSnackBar('فشل في إصلاح المشكلة: ${e.toString()}');
    }
  }

  Future<bool> _fixIssue(DataIntegrityIssue issue) async {
    switch (issue.module) {
      case 'products':
        return await ref.read(enhancedProductManagementProvider.notifier).fixDataIntegrityIssues();
      case 'categories':
        return await ref.read(enhancedCategoryManagementProvider.notifier).fixDataIntegrityIssues();
      case 'units':
        return await ref.read(enhancedUnitManagementProvider.notifier).fixDataIntegrityIssues();
      default:
        return false;
    }
  }

  void _exportReport() {
    // TODO: Implement export functionality
    context.showInfoSnackBar('تصدير التقرير قيد التطوير');
  }
}

class DataIntegrityIssue {
  final String id;
  final String module;
  final String title;
  final String description;
  final IssueSeverity severity;
  final bool canAutoFix;
  final List<String> affectedItems;
  final String recommendation;

  DataIntegrityIssue({
    required this.id,
    required this.module,
    required this.title,
    required this.description,
    required this.severity,
    required this.canAutoFix,
    required this.affectedItems,
    required this.recommendation,
  });

  factory DataIntegrityIssue.fromMap(Map<String, dynamic> map, String module) {
    return DataIntegrityIssue(
      id: '${module}_${DateTime.now().millisecondsSinceEpoch}',
      module: module,
      title: map['message'] as String? ?? 'مشكلة غير محددة',
      description: map['details'] as String? ?? map['message'] as String? ?? '',
      severity: _mapSeverity(map['type'] as String?),
      canAutoFix: map['can_fix'] as bool? ?? true,
      affectedItems: [map['item_id'] as String? ?? ''].where((item) => item.isNotEmpty).toList(),
      recommendation: map['recommendation'] as String? ?? 'يُنصح بمراجعة البيانات يدوياً',
    );
  }

  static IssueSeverity _mapSeverity(String? type) {
    switch (type) {
      case 'critical':
      case 'error':
        return IssueSeverity.critical;
      case 'warning':
        return IssueSeverity.warning;
      default:
        return IssueSeverity.info;
    }
  }
}

enum IssueSeverity {
  critical,
  warning,
  info,
}
