import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/theme/colors.dart';
import '../../core/theme/text_styles.dart';
import '../../widgets/main_layout.dart';
import 'add_item_dialog.dart';

class SaleAddPage extends ConsumerStatefulWidget {
  const SaleAddPage({super.key});

  @override
  ConsumerState<SaleAddPage> createState() => _SaleAddPageState();
}

class _SaleAddPageState extends ConsumerState<SaleAddPage> {
  final _invoiceNumberController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime _date = DateTime.now();
  final List<Map<String, dynamic>> _items = [];

  /* ---------- Build ---------- */
  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'فاتورة جديدة',
      actions: [
        ElevatedButton.icon(
          onPressed: _saveInvoice,
          icon: const Icon(Icons.save),
          label: const Text('حفظ'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.success,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20.r)),
          ),
        ),
      ],
      child: Column(
        children: [
          _buildHeader(),
          _buildItemsSection(),
          _buildSummary(),
        ],
      ),
    );
  }

  /* ---------- Header ---------- */
  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(child: _buildTextField(_invoiceNumberController, 'رقم الفاتورة')),
          SizedBox(width: 16.w),
          Expanded(child: _buildTextField(_notesController, 'ملاحظات')),
          SizedBox(width: 16.w),
          ElevatedButton.icon(
            onPressed: () async => _date = (await showDatePicker(
                  context: context,
                  initialDate: _date,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now(),
                )) ??
                _date,
            icon: const Icon(Icons.calendar_today),
            label: Text('${_date.day}/${_date.month}/${_date.year}'),
          ),
        ],
      ),
    );
  }

  /* ---------- الأصناف ---------- */
  Widget _buildItemsSection() {
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(16.r),
            child: Row(
              children: [
                const Text('قائمة الأصناف', style: TextStyle(fontWeight: FontWeight.bold)),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add),
                  label: const Text('إضافة صنف'),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _items.length,
              itemBuilder: (_, index) => _itemRow(_items[index], index),
            ),
          ),
        ],
      ),
    );
  }

  /* ---------- Summary ---------- */
  Widget _buildSummary() {
    final total = _items.fold<double>(0, (s, e) => s + e['total']);
    return Container(
      padding: EdgeInsets.all(16.r),
      color: Colors.grey[900],
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Text('الإجمالي: $total', style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
          ElevatedButton(onPressed: _saveInvoice, child: const Text('حفظ الفاتورة')),
        ],
      ),
    );
  }

  /* ---------- Rows ---------- */
  Widget _itemRow(Map item, int index) => Card(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
        child: ListTile(
          title: Text(item['name']),
          subtitle: Text('${item['qty']} × ${item['price']} = ${item['total']}'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(icon: const Icon(Icons.edit), onPressed: () => _editItem(index)),
              IconButton(icon: const Icon(Icons.delete), onPressed: () => _deleteItem(index)),
            ],
          ),
        ),
      );

  /* ---------- Actions ---------- */
  void _addItem() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (_) => const AddItemDialog(),
    );
    if (result != null) setState(() => _items.add(result));
  }

  void _editItem(int index) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (_) => AddItemDialog(initial: _items[index]),
    );
    if (result != null) setState(() => _items[index] = result);
  }

  void _deleteItem(int index) => setState(() => _items.removeAt(index));

  void _saveInvoice() {
    // TODO: حفظ الفاتورة في الـ Repository
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم حفظ الفاتورة بنجاح')),
    );
  }

  Widget _buildTextField(TextEditingController controller, String label) => TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.r)),
        ),
      );
}