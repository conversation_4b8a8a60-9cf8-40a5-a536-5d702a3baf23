import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:tijari_tech/data/local/delete_db.dart';

import 'app.dart';
import 'core/auth/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // await deleteDatabaseFile();

  // تهيئة sqflite للعمل على سطح المكتب
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // حذف قاعدة البيانات القديمة وإعادة إنشائها

// await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(false);

// تهيئة قاعدة البيانات
  await initializeDatabase();
  // تهيئة خدمة المصادقة
  // await AuthService.init();

  runApp(
    const ProviderScope(
      child: TijariApp(),
    ),
  );
}
