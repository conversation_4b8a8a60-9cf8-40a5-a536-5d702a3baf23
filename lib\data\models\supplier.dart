import 'package:json_annotation/json_annotation.dart';

part 'supplier.g.dart';

@JsonSerializable()
class Supplier {
  final String id;
  final String name;
  final String? phone;
  final String? email;
  final double balance;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final bool isSynced;

  const Supplier({
    required this.id,
    required this.name,
    this.phone,
    this.email,
    this.balance = 0.0,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  // Factory constructor for creating a new Supplier instance from a map
  factory Supplier.fromMap(Map<String, dynamic> map) {
    return Supplier(
      id: map['id'] as String,
      name: map['name'] as String,
      phone: map['phone'] as String?,
      email: map['email'] as String?,
      balance: (map['balance'] as num?)?.toDouble() ?? 0.0,
      notes: map['notes'] as String?,
      createdAt: map['created_at'] != null 
          ? DateTime.parse(map['created_at'] as String)
          : null,
      updatedAt: map['updated_at'] != null 
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      deletedAt: map['deleted_at'] != null 
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
      isSynced: (map['is_synced'] as int? ?? 0) == 1,
    );
  }

  // Convert Supplier instance to a map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'balance': balance,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
    };
  }

  // JSON serialization
  factory Supplier.fromJson(Map<String, dynamic> json) => _$SupplierFromJson(json);
  Map<String, dynamic> toJson() => _$SupplierToJson(this);

  // Copy with method for creating modified copies
  Supplier copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    double? balance,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return Supplier(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  // Equality and hashCode
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Supplier && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // String representation
  @override
  String toString() {
    return 'Supplier(id: $id, name: $name, phone: $phone, balance: $balance, isSynced: $isSynced)';
  }

  // Helper methods
  bool get isDeleted => deletedAt != null;
  bool get isActive => !isDeleted;
  bool get hasPhone => phone != null && phone!.isNotEmpty;
  bool get hasEmail => email != null && email!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  bool get hasDebt => balance > 0; // نحن مدينون للمورد
  bool get hasCredit => balance < 0; // المورد مدين لنا
  bool get isBalanced => balance == 0;
  
  // Get balance status
  SupplierBalanceStatus get balanceStatus {
    if (balance > 0) return SupplierBalanceStatus.weOwe; // نحن مدينون
    if (balance < 0) return SupplierBalanceStatus.theyOwe; // هم مدينون
    return SupplierBalanceStatus.balanced;
  }
  
  // Get absolute balance
  double get absoluteBalance => balance.abs();
  
  // Create a new supplier with current timestamp
  static Supplier create({
    required String id,
    required String name,
    String? phone,
    String? email,
    double balance = 0.0,
    String? notes,
  }) {
    final now = DateTime.now();
    return Supplier(
      id: id,
      name: name,
      phone: phone,
      email: email,
      balance: balance,
      notes: notes,
      createdAt: now,
      updatedAt: now,
      isSynced: false,
    );
  }

  // Update supplier with new timestamp
  Supplier update({
    String? name,
    String? phone,
    String? email,
    double? balance,
    String? notes,
  }) {
    return copyWith(
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      balance: balance ?? this.balance,
      notes: notes ?? this.notes,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Add to balance (increase what we owe or decrease what they owe)
  Supplier addToBalance(double amount) {
    return copyWith(
      balance: balance + amount,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Subtract from balance (decrease what we owe or increase what they owe)
  Supplier subtractFromBalance(double amount) {
    return copyWith(
      balance: balance - amount,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Set balance to zero
  Supplier clearBalance() {
    return copyWith(
      balance: 0.0,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as deleted
  Supplier markAsDeleted() {
    return copyWith(
      deletedAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  // Mark as synced
  Supplier markAsSynced() {
    return copyWith(isSynced: true);
  }
  
  // Remove phone
  Supplier removePhone() {
    return copyWith(
      phone: null,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }
  
  // Remove email
  Supplier removeEmail() {
    return copyWith(
      email: null,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }
  
  // Remove notes
  Supplier removeNotes() {
    return copyWith(
      notes: null,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }
}

// Supplier balance status enum
enum SupplierBalanceStatus {
  weOwe,    // نحن مدينون للمورد
  theyOwe,  // المورد مدين لنا
  balanced, // متوازن
}

// Extension for supplier balance status
extension SupplierBalanceStatusExtension on SupplierBalanceStatus {
  String get displayName {
    switch (this) {
      case SupplierBalanceStatus.weOwe:
        return 'لنا عليه';
      case SupplierBalanceStatus.theyOwe:
        return 'له علينا';
      case SupplierBalanceStatus.balanced:
        return 'متوازن';
    }
  }
  
  String get displayNameEn {
    switch (this) {
      case SupplierBalanceStatus.weOwe:
        return 'We Owe';
      case SupplierBalanceStatus.theyOwe:
        return 'They Owe';
      case SupplierBalanceStatus.balanced:
        return 'Balanced';
    }
  }
}
