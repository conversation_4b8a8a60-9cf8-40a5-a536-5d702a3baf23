import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/text_styles.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../data/models/stock.dart';
import '../../../providers/stock_provider.dart';
import '../../../providers/warehouse_provider.dart';

/// حوار نقل المخزون
///
/// يوفر واجهة لنقل المخزون بين المخازن مع:
/// - اختيار المخزن المصدر والوجهة
/// - تحديد الكمية المراد نقلها
/// - التحقق من توفر الكمية
/// - تسجيل سبب النقل والملاحظات
///
/// Ref: تم تطوير هذا الحوار لضمان دقة عمليات نقل المخزون وتتبعها

class StockTransferDialog extends ConsumerStatefulWidget {
  final Stock? stock;

  const StockTransferDialog({
    super.key,
    this.stock,
  });

  @override
  ConsumerState<StockTransferDialog> createState() =>
      _StockTransferDialogState();
}

class _StockTransferDialogState extends ConsumerState<StockTransferDialog> {
  final _formKey = GlobalKey<FormState>();
  final _quantityController = TextEditingController();
  final _reasonController = TextEditingController();
  final _notesController = TextEditingController();

  String? selectedFromWarehouse;
  String? selectedToWarehouse;
  String? selectedProduct;

  @override
  void initState() {
    super.initState();
    if (widget.stock != null) {
      selectedFromWarehouse = widget.stock!.warehouseId;
      selectedProduct = widget.stock!.productId;
    }
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _reasonController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(stockManagementProvider);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        padding: EdgeInsets.all(24.w),
        constraints: BoxConstraints(
          maxWidth: 450.w,
          maxHeight: 700.h,
        ),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      Icons.swap_horiz,
                      color: AppColors.primary,
                      size: 24.sp,
                    ),
                    SizedBox(width: 8.w),
                    Text(
                      'نقل المخزون',
                      style: AppTextStyles.titleLarge.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),

                SizedBox(height: 24.h),

                // Product info
                if (widget.stock != null) ...[
                  Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'المنتج',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          widget.stock!.productName ?? 'منتج غير محدد',
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          'الكمية المتاحة',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          '${widget.stock!.actualAvailableQuantity.toStringAsFixed(2)} ${widget.stock!.unitName ?? ''}',
                          style: AppTextStyles.bodyMedium.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 16.h),
                ],

                // From warehouse
                Text(
                  'من المخزن',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 8.h),
                Consumer(
                  builder: (context, ref, child) {
                    final warehousesAsync = ref.watch(activeWarehousesProvider);

                    return warehousesAsync.when(
                      data: (warehouses) => DropdownButtonFormField<String>(
                        value: selectedFromWarehouse,
                        decoration: InputDecoration(
                          hintText: 'اختر المخزن المصدر',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 8.h,
                          ),
                        ),
                        items: warehouses
                            .map((warehouse) => DropdownMenuItem<String>(
                                  value: warehouse.id,
                                  child: Text(warehouse.name),
                                ))
                            .toList(),
                        onChanged: widget.stock != null
                            ? null
                            : (value) {
                                setState(() {
                                  selectedFromWarehouse = value;
                                });
                              },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى اختيار المخزن المصدر';
                          }
                          return null;
                        },
                      ),
                      loading: () => const CircularProgressIndicator(),
                      error: (error, stack) => Text('خطأ في تحميل المخازن'),
                    );
                  },
                ),

                SizedBox(height: 16.h),

                // To warehouse
                Text(
                  'إلى المخزن',
                  style: AppTextStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 8.h),
                Consumer(
                  builder: (context, ref, child) {
                    final warehousesAsync = ref.watch(activeWarehousesProvider);

                    return warehousesAsync.when(
                      data: (warehouses) => DropdownButtonFormField<String>(
                        value: selectedToWarehouse,
                        decoration: InputDecoration(
                          hintText: 'اختر المخزن الوجهة',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12.w,
                            vertical: 8.h,
                          ),
                        ),
                        items: warehouses
                            .where((w) => w.id != selectedFromWarehouse)
                            .map((warehouse) => DropdownMenuItem<String>(
                                  value: warehouse.id,
                                  child: Text(warehouse.name),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedToWarehouse = value;
                          });
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى اختيار المخزن الوجهة';
                          }
                          if (value == selectedFromWarehouse) {
                            return 'لا يمكن أن يكون المخزن المصدر والوجهة نفسهما';
                          }
                          return null;
                        },
                      ),
                      loading: () => const CircularProgressIndicator(),
                      error: (error, stack) => Text('خطأ في تحميل المخازن'),
                    );
                  },
                ),

                SizedBox(height: 16.h),

                // Quantity
                CustomTextField(
                  controller: _quantityController,
                  label: 'الكمية المراد نقلها',
                  hint: 'أدخل الكمية',
                  keyboardType: TextInputType.number,
                  prefixIcon: Icons.inventory,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال الكمية';
                    }
                    final quantity = double.tryParse(value);
                    if (quantity == null || quantity <= 0) {
                      return 'يرجى إدخال كمية صحيحة';
                    }
                    if (widget.stock != null &&
                        quantity > widget.stock!.actualAvailableQuantity) {
                      return 'الكمية أكبر من المتاح';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16.h),

                // Reason
                CustomTextField(
                  controller: _reasonController,
                  label: 'سبب النقل',
                  hint: 'أدخل سبب نقل المخزون',
                  prefixIcon: Icons.info_outline,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال سبب النقل';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16.h),

                // Notes
                CustomTextField(
                  controller: _notesController,
                  label: 'ملاحظات (اختياري)',
                  hint: 'أدخل أي ملاحظات إضافية',
                  prefixIcon: Icons.note,
                  maxLines: 3,
                ),

                SizedBox(height: 32.h),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: Text(
                          'إلغاء',
                          style: AppTextStyles.bodyMedium,
                        ),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: state.isLoading ? null : _transferStock,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          padding: EdgeInsets.symmetric(vertical: 12.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                        ),
                        child: state.isLoading
                            ? SizedBox(
                                height: 20.h,
                                width: 20.w,
                                child: const CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                'نقل',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _transferStock() async {
    if (!_formKey.currentState!.validate()) return;
    if (selectedFromWarehouse == null || selectedToWarehouse == null) return;

    final quantity = double.parse(_quantityController.text);
    final reason = _reasonController.text;
    final notes = _notesController.text;
    final productId = widget.stock?.productId ?? selectedProduct;

    if (productId == null) return;

    try {
      await ref.read(stockManagementProvider.notifier).transferStock(
            productId: productId,
            fromWarehouseId: selectedFromWarehouse!,
            toWarehouseId: selectedToWarehouse!,
            quantity: quantity,
            unitCost: widget.stock?.averageCost ?? 0.0,
            notes: notes,
            reason: reason,
            userId: 'current-user', // TODO: Get from auth provider
          );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نقل المخزون بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في نقل المخزون: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
